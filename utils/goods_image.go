package utils

import (
	"fmt"
	"os"
	"strings"

	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/ppkg/kit/cast"
)

// 缩略图尺寸类型，值为60,240,360,1280
var imageSizes = []int64{60, 240, 360, 1280}

const (
	DEFAULT_GOODS_IMAGE = "/www/shop/store/goods/default_goods_image.gif"

	ATTACH_GOODS = "shop/store/goods"

	// 阿里云oss配置
	ALIOSS_IMAGE_URL = "https://oss.upetmart.com"
)

// 获取商品图片
func HandleGoodsImage(img string, goodsCommonId, colorId, storeId int64) string {
	if strings.HasPrefix(img, "https://") || strings.HasPrefix(img, "http://") {
		return img
	}
	if len(img) == 0 {
		return ALIOSS_IMAGE_URL + strings.Replace(DEFAULT_GOODS_IMAGE, ".", fmt.Sprintf("_%d.", 1280), -1)
	}

	return cthumb(img, 1280, storeId)
}

func cthumb(img string, size, storeId int64) string {
	if !InArray(size, imageSizes) {
		size = 240
	}
	if strings.Index(img, "?watermark/3") != -1 || strings.Index(img, "?x-oss-process=") != -1 {
		return getAliOssImageUrl(fmt.Sprintf("%s/%d/%s", ATTACH_GOODS, storeId, img), size)
	}

	pathArr := strings.Split(strings.Split(img, "?")[0], "/")
	baseName := pathArr[len(pathArr)-1]
	if strings.Index(baseName, "_") > 0 {
		if sid := cast.ToInt64(strings.Split(baseName, "_")[0]); sid > 0 {
			storeId = sid
		}
	}
	return getAliOssImageUrl(fmt.Sprintf("%s/%d/%s", ATTACH_GOODS, storeId, img), size)
}

func getAliOssImageUrl(img string, size int64) string {
	glog.Info("ASPNETCORE_ENVIRONMENT: ", os.Getenv("ASPNETCORE_ENVIRONMENT"))
	env := "www"
	// 原php项目的图片路径配置是 sit1、uat1
	if kit.EnvIsTest() {
		env = "sit1"
	} else if kit.EnvIsUAT() {
		env = "uat1"
	}
	imgUrl := fmt.Sprintf("%s/%s/%s", ALIOSS_IMAGE_URL, env, img)
	if size > 0 && strings.Index(img, "?x-oss-process=") == -1 {
		imgUrl += fmt.Sprintf("?x-oss-process=style/%d", size)
	}
	return imgUrl
}
