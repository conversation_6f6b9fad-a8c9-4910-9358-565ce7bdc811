package utils

import "testing"

func Test_cthumb(t *testing.T) {
	type args struct {
		img     string
		size    int64
		storeId int64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				img:     "",
				size:    240,
				storeId: 1,
			},
		},
		{
			args: args{
				img:     "2022/1_07096600504128629.jpg",
				size:    1280,
				storeId: 1,
			},
		},
		{
			args: args{
				img:     "2021/1_06658594038084213.png?x-oss-process=image/watermark,image_dXBsb2Fkcy8yMDIxLTExLTMwLzE5N2Y5M2I5MzYwNTEyMDhiZWVmOWNlZjAzZjkzN2U4LnBuZz94LW9zcy1wcm9jZXNzPWltYWdlL3Jlc2l6ZSxQXzEwMA==,t_100,g_sw,x_0,y_0,voffset_0/watermark,text_NS45OQ==,type_d3F5LXplbmhlaQ==,color_af7832,size_50,shadow_0,rotate_0,fill_0,t_100,g_sw,x_50,y_30,voffset_0",
				size:    1280,
				storeId: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := cthumb(tt.args.img, tt.args.size, tt.args.storeId)
			t.Log(tt.args.img, got)
		})
	}
}
