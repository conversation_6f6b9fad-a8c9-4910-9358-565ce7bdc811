package utils

import (
	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
	"strconv"
)

//redis链接
func ConnectRedis() *redis.Client {
	var db = config.GetString("redis.DB")
	var addr = config.GetString("redis.Addr")
	var pwd = config.GetString("redis.Password")
	DB, _ := strconv.Atoi(db)
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: pwd,
		DB:       DB,
	})
	_, err := client.Ping().Result()
	if err != nil {
		panic(err)
	}
	return client
}

////集群
//func ConnectRedis() *redis.ClusterClient {
//	address := config.GetString("redis.cluster")
//	pwd := config.GetString("redis.cluster.pwd")
//	addrs := strings.Split(address, "|")
//
//	client := redis.NewClusterClient(&redis.ClusterOptions{
//		Addrs:    addrs,
//		Password: pwd,
//	})
//	_, err := client.Ping().Result()
//	if err != nil {
//		panic(err)
//	}
//	return client
//}
//
////根据key模糊获取redis集群(多主多从)的所有key
//func ClusterRedisKey(Patternkeys string, client *redis.ClusterClient) []string {
//	pwd := config.GetString("redis.cluster.pwd")
//	clusterSlots, err := client.ClusterSlots().Result()
//	if err != nil {
//		return []string{}
//	}
//	allKey := make([]string, 0)
//	for _, v := range clusterSlots {
//		vclient := redis.NewClient(&redis.Options{
//			Addr:     v.Nodes[0].Addr,
//			Password: pwd,
//		})
//		hv := vclient.Keys(Patternkeys).Val()
//		for _, v := range hv {
//			allKey = append(allKey, v)
//		}
//		vclient.Close()
//	}
//	return allKey
//}

// 通过key获取hash的元素值
func HashGet(key, field string) string {
	client := ConnectRedis()
	val, err := client.HGet(key, field).Result()
	if err == redis.Nil {
		return ""
	} else if err != nil {
		return ""
	}
	return val
}
