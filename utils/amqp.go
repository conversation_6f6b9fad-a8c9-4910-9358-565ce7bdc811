package utils

import (
	"bytes"
	"fmt"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/streadway/amqp"
)

//开启一个mq链接
func NewMqConn() *amqp.Connection {
	mqEndPoint := config.GetString("mq.EndPoint")
	mqUserName := config.GetString("mq.UserName")
	mqPassWord := config.GetString("mq.PassWord")
	mqHostName := config.GetString("mq.HostName")

	var buf bytes.Buffer
	buf.WriteString("amqp://")
	buf.WriteString(mqUserName)
	buf.WriteString(":")
	buf.WriteString(mqPassWord)

	// <Your End Point> 请从控制台获取。如果你使用的是杭州Region，那么Endpoint会形如 137000000010111.mq-amqp.cn-hangzhou-a.aliyuncs.com
	buf.WriteString("@")
	buf.WriteString(mqHostName)
	buf.WriteString("/")
	buf.WriteString(mqEndPoint)

	url := buf.String()
	//url = "************************************/" //临时写死 配置中心加不了配置
	url = config.GetString("mq.oneself")
	glog.Info("RabbitMQ地址：", url)
	conn, err := amqp.Dial(url)
	if err != nil {
		glog.Error("RabbitMQ dial fail. err : ", err)
		panic(err)
	}
	return conn
}

func NewMqChannel(conn *amqp.Connection) *amqp.Channel {
	channel, err := conn.Channel()
	if err != nil {
		glog.Error("RabbitMQ Get Channel fail. err : ", err)
		panic(err)
	}
	return channel
}

/***************************************MQ********************************************/
//发布推送消息到RabbitMQ，参数：队列名和内容
func PublishRabbitMQ(queue, content, exchange string) bool {
	defer func() {
		if err := recover(); err != nil {
			glog.Error(err)
		}
	}()

	glog.Info(fmt.Sprintf("推送MQ开始！队列名，%s，内容：%s，Exchange：%s", queue, content, exchange))
	//开启连接
	conn := NewMqConn()
	defer conn.Close()
	ch := NewMqChannel(conn)
	defer ch.Close()

	// 定义交换“direct”、“fanout”、“topic”和“headers”
	if err := ch.ExchangeDeclare(exchange, amqp.ExchangeDirect, true, false, false, false, nil); nil != err {
		glog.Error("RabbitMQ ExchangeDeclare fail, err : ", err)
		return false
	}
	// name,durable,delete when unused,exclusive,no-wait,arguments
	q, err := ch.QueueDeclare(queue, true, false, false, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueDeclare fail, err : ", q.Name, err)
		return false
	}
	err = ch.QueueBind(queue, queue, exchange, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueBind fail, err : ", err)
		return false
	}

	_publishing := amqp.Publishing{
		ContentType:  "text/plain",
		Body:         []byte(content),
		DeliveryMode: amqp.Persistent, // Transient 性能好，但是会丢数据。故用 Persistent
	}
	//a.b.c.d.e 为发布key,以.分割；
	if err := ch.Publish(exchange, queue, false, false, _publishing); nil != err {
		glog.Error("RabbitMQ Publish fail, err : ", err)
		return false
	}
	glog.Info(fmt.Sprintf("推送MQ结束！队列名，%s，内容：%s，Exchange：%s", queue, content, exchange))
	return true
}

//订阅mq消息
func SubscribeRabbitMQ(queue, exchange string) ([]byte, error) {
	defer func() {
		if err := recover(); err != nil {
			glog.Error(err)
		}
	}()

	glog.Info(fmt.Sprintf("订阅MQ开始！队列名，%s，Exchange：%s", queue, exchange))
	//开启链接
	conn := NewMqConn()
	defer conn.Close()
	ch := NewMqChannel(conn)
	defer ch.Close()

	err := ch.ExchangeDeclare(exchange, "direct", true, false, false, false, nil)
	if err != nil {
		glog.Error("RabbitMQ ExchangeDeclare fail, err : ", err)
		return nil, err
	}
	// name// durable// delete when unused // exclusive// no-wait // arguments
	q, err := ch.QueueDeclare(queue, true, false, false, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueDeclare fail, err : ", q.Name, err)
		return nil, err
	}
	err = ch.QueueBind(queue, queue, exchange, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueBind fail, err : ", q.Name, err)
		return nil, err
	}

	if delivery, err := ch.Consume(queue, queue, false, false, false, false, nil); err != nil {
		glog.Error("RabbitMQ Consume fail, err : ", q.Name, err)
		return nil, err
	} else {
		for d := range delivery {
			func() {
				defer func() {
					if err := recover(); err != nil {
						glog.Error(err)
					}
				}()
				//todo 处理业务逻辑
				fmt.Println(d)

			}()
		}
	}
	return nil, nil
}
