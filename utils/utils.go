package utils

import (
	"_/models"
	"bytes"
	"context"
	"crypto/md5"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"hash/crc32"
	"io"
	"io/ioutil"
	"log"
	"math"
	"math/rand"
	"net"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"google.golang.org/grpc/metadata"

	"github.com/go-redis/redis"
	"github.com/google/uuid"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/shopspring/decimal"
)

const (
	DATATIME_LAYOUT      = "2006-01-02 15:04:05"
	DATA_LAYOUT          = "2006-01-02"
	DATA_LAYOUT_SHORT    = "01月02日"
	TIME_LAYOUT          = "15:04:05"
	TIME_LAYOUT_SHORT    = "15:04"
	CODE_SUCCESS         = 200
	CODE_BUSINESSERROR   = 300
	CODE_SERVEREXCEPTION = 400
)

func ConvertDoubleFloat(value float64) float64 {
	value, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", value), 64)
	return value
}

//PrintJSON 将struct序列化json打印日志
func PrintJSON(inter interface{}) {
	bt, _ := json.Marshal(inter)
	log.Println("json", string(bt))
}

//生成32位md5字串
func GetMd5String(s string) string {
	h := md5.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

//生成32位Guid字串
func GetGuid() string {
	return strings.ReplaceAll(uuid.New().String(), "-", "")
}

//生成36位Guid字串
func GetGuid36() string {
	return uuid.New().String()
}

//GenCode 生成编号，前缀+随机数（或者指定的某个数字）+数量（数量位数不足5，会前置补0）
func GenCode(prefix string, rand int, count int) string {
	var strs []string
	strs = append(strs, prefix)
	strs = append(strs, time.Now().Local().Format("20060102150405"))
	strs = append(strs, strconv.Itoa(rand))
	strCount := strconv.Itoa(int(count))
	for index := 0; index < 5-len(strCount); index++ {
		strs = append(strs, "0")
	}
	strs = append(strs, strCount)
	return strings.Join(strs, "")
}

//获取签名(正向接口)
//par: 应用参数的JSON ，methodName 调用API的名称,返回拼接好的
func Sign(par string, methodName string, tokenStr string, app_key string, appSecret string) string {

	format := "json"
	method := methodName
	sign_method := "md5"
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	token := tokenStr
	v := "1.0"
	body := par

	var slice1 []string
	//slice1 := make([]string, ParLen)
	slice1 = append(slice1, "app_key"+app_key)
	slice1 = append(slice1, "format"+format)
	slice1 = append(slice1, "method"+method)
	slice1 = append(slice1, "sign_method"+sign_method)
	slice1 = append(slice1, "timestamp"+timestamp)
	slice1 = append(slice1, "v"+v)

	//如果是获取token的API就不需要TOKEN参数
	if methodName != "emall.token.get" {
		slice1 = append(slice1, "token"+token)
	}
	sort.Strings(slice1)
	content := appSecret
	for i := 0; i < len(slice1); i++ {
		content += slice1[i]
	}
	content += body
	content += appSecret
	sgin := strings.ToUpper(GetMd5String(content))
	retrunSgin := "method=" + method + "&timestamp=" + url.QueryEscape(timestamp) + "&format=" + format + "&app_key=" + app_key + "&v=" + v + "&sign_method=" + sign_method
	if methodName != "emall.token.get" {
		retrunSgin += "&token=" + token
	}
	retrunSgin += "&sign=" + sgin
	return retrunSgin
}

//获取签名(反向接口) 只返回sgin值
//传所有请求参数放入map
func SignFX(par map[string]string) string {

	appSecret := par["appSecret"]
	var slice1 []string

	for k, v := range par {
		if k != "sign" {
			slice1 = append(slice1, k+v)
		}
	}
	sort.Strings(slice1)
	content := appSecret
	for i := 0; i < len(slice1); i++ {
		content += slice1[i]
	}
	content += appSecret
	sgin := strings.ToUpper(GetMd5String(content))

	return sgin
}

//获取随机字符串，指定长度
func GetRandomString(l int) string {
	str := "0123456789abcdefghijklmnopqrstuvwxyz"
	bytes := []byte(str)
	result := []byte{}
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < l; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}

//全角转换半角
func DBCtoSBC(s string) string {
	retstr := ""
	for _, i := range s {
		inside_code := i
		if inside_code == 12288 {
			inside_code = 32
		} else {
			inside_code -= 65248
		}
		if inside_code < 32 || inside_code > 126 {
			retstr += string(i)
		} else {
			retstr += string(inside_code)
		}
	}
	return retstr
}

func HttpPost(url string, bytesData []byte, contentType string) ([]byte, error) {
	//跳过证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		DialContext: (&net.Dialer{
			Timeout: 10 * time.Second,
		}).DialContext,
	}

	reader := bytes.NewReader(bytesData)
	request, err := http.NewRequest("POST", url, reader)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}
	if len(contentType) > 0 {
		request.Header.Set("Content-Type", contentType) //"application/json;charset=UTF-8"
	}
	client := &http.Client{Transport: tr}
	resp, err := client.Do(request)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}
	if resp.StatusCode != 200 {
		fmt.Println(resp.Status)
		return nil, errors.New(resp.Status)
	}
	respBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}
	return respBytes, nil
}

//查询切片是否包含值
func IsValueInList(value int, list []int) bool {
	for _, v := range list {
		if v == value {
			return true
		}
	}
	return false
}

//查询切片是否包含值
func IsValueInListStr(value string, list []string) bool {
	if len(list) == 0 {
		return false
	}
	for _, v := range list {
		if v == value {
			return true
		}
	}
	return false
}

// 发送GET请求
// url：         请求地址
// response：    请求返回的内容
func HttpGet(url string) string {

	// 超时时间：5秒
	client := &http.Client{Timeout: 120 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	var buffer [512]byte
	result := bytes.NewBuffer(nil)
	for {
		n, err := resp.Body.Read(buffer[0:])
		result.Write(buffer[0:n])
		if err != nil && err == io.EOF {
			break
		} else if err != nil {
			panic(err)
		}
	}

	return result.String()
}

//子龙请求
//dataJson : 数据对象转化成json字符串
func HttpPostZl(url string, dataJson []byte, Headers string) ([]byte, error) {
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(dataJson))
	client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	req.Header.Set("Content-Type", "application/json")

	if len(Headers) > 0 {
		strlist := strings.Split(Headers, "&")
		for i := 0; i < len(strlist); i++ {
			v := strlist[i]
			valuelist := strings.Split(v, "|")
			req.Header.Set(valuelist[0], valuelist[1])
		}
	}

	for k, v := range BjSignMap(url) {
		req.Header.Set(k, v)
	}

	res, err := client.Do(req)
	if err != nil {
		log.Println(err)
		return []byte(""), err
	}
	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	return body, err
}

func BjSignMap(url string) map[string]string {
	domainUrl := strings.Split(url, "//")[1]
	baseUrl := strings.Split(domainUrl, "/")[0]
	method := strings.Split(url, baseUrl)[1]
	Timestamp := strconv.Itoa(int(time.Now().Unix()))
	sign := fmt.Sprintf("AppId=%s&Secret=%s&Url=%s&Timestamp=%s&Version=%s", config.GetString("BJAuth.AppId"), config.GetString("BJAuth.Secret"), method, Timestamp, config.GetString("BJAuth.Version"))
	h := md5.New()
	h.Write([]byte(sign))
	md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	arr := make(map[string]string)
	arr["focus-auth-appid"] = config.GetString("BJAuth.AppId")
	arr["focus-auth-userid"] = "0"
	arr["focus-auth-username"] = "0"
	arr["focus-auth-version"] = config.GetString("BJAuth.Version")
	arr["focus-auth-url"] = method
	arr["focus-auth-timestamp"] = Timestamp
	arr["focus-auth-sign"] = md5sign
	return arr
}

//处理redis的setnx的返回结果。如果锁定时间已经超过默认时间5分钟，则自动删除。默认时间可更改
func DelRedisSetNx(redisConn *redis.Client, redisKey string, timeMinute int32) bool {
	if redisConn.Exists(redisKey).Val() > 0 {
		timeUnix, _ := strconv.Atoi(redisConn.Get(redisKey).Val())
		//与当前时间比较
		timeNowUnix := time.Now().Add(-1 * time.Minute * 5).Unix() // 5分钟
		if timeMinute > 0 {
			timeDuration := time.Duration(-1*timeMinute) * time.Minute
			timeNowUnix = time.Now().Add(timeDuration).Unix()
		}
		if timeNowUnix >= int64(timeUnix) {
			//超过5分钟，则自动删除
			redisConn.Del(redisKey)
			return true
		}
		return false
	}
	return true
}

// 计算地球上两点间距离
func EarthDistance(lat1, lng1, lat2, lng2 float64) float64 {
	radius := 6371.0 // 6378137
	rad := math.Pi / 180.0

	lat1 = lat1 * rad
	lng1 = lng1 * rad
	lat2 = lat2 * rad
	lng2 = lng2 * rad

	theta := lng2 - lng1
	dist := math.Acos(math.Sin(lat1)*math.Sin(lat2) + math.Cos(lat1)*math.Cos(lat2)*math.Cos(theta))

	return dist * radius
}

// 获取json 字符串
func GetJsonString(object interface{}) string {
	b, err := json.Marshal(object)
	if err != nil {
		glog.Error(err)
		return ""
	} else {
		return string(b)
	}
}

//返回hash int
func HashInt(s string) int {
	v := int(crc32.ChecksumIEEE([]byte(s)))
	if v >= 0 {
		return v
	}
	if -v >= 0 {
		return -v
	}
	// v == MinInt
	return 0
}

// 获取grpc中context传递的信息
func LoadGrpcContext(ctx context.Context) *models.GrpcContext {
	var GrpcContext models.GrpcContext
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if len(md.Get("grpc_context")) > 0 {
			if err := json.Unmarshal([]byte(md.Get("grpc_context")[0]), &GrpcContext); err != nil {
				glog.Error(err)
			}
		} else {
			glog.Error("grpc_context未获取到信息")
		}
	} else {
		_, str, isOk := metadata.FromOutgoingContextRaw(ctx)
		if isOk {
			if err := json.Unmarshal([]byte(str[0][1]), &GrpcContext); err != nil {
				glog.Error(err)
			}
		} else {
			glog.Error("grpc context 加载用户登录信息失败")
		}

	}

	return &GrpcContext
}

//start：正数 - 在字符串的指定位置开始,超出字符串长度强制把start变为字符串长度
//       负数 - 在从字符串结尾的指定位置开始
//       0 - 在字符串中的第一个字符处开始
//length:正数 - 从 start 参数所在的位置返回
//       负数 - 从字符串末端返回
func Substr(str string, start, length int) string {
	if length == 0 {
		return ""
	}
	runeStr := []rune(str)
	lenStr := len(runeStr)

	if start < 0 {
		start = lenStr + start
	}
	if start > lenStr {
		start = lenStr
	}
	end := start + length
	if end > lenStr {
		end = lenStr
	}
	if length < 0 {
		end = lenStr + length
	}
	if start > end {
		start, end = end, start
	}
	return string(runeStr[start:end])
}

func InArray(i int64, arr []int64) bool {
	for _, v := range arr {
		if v == i {
			return true
		}
	}
	return false
}

// 计算商品会员价member_price = round(goodsPrice * memberPrice * 0.1)
func CalculateMemberPrice(goodsPrice, memberPrice float64) float64 {
	f, _ := decimal.NewFromFloat(goodsPrice * memberPrice * 0.1).Round(2).Float64()
	return f
}

// 手机号中间位数隐藏
func HiddenMobile(mobile string) string {
	if len(mobile) > 8 {
		return fmt.Sprintf("%s***%s", mobile[0:4], mobile[7:])
	}
	return mobile
}
