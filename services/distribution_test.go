package services

import (
	"_/proto/sh"
	"context"
	"github.com/golang/protobuf/ptypes/wrappers"
	kit "github.com/tricobbler/rp-kit"
	"testing"
)

func TestDistribution_Categories(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisCategoriesRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisCategoriesResponse
		wantErr bool
	}{
		{
			args: args{
				in: &sh.DisCategoriesRequest{ParentId: 0},
			},
		}, {
			args: args{
				in: &sh.DisCategoriesRequest{ParentId: 1037},
			},
		}, {
			args: args{
				in: &sh.DisCategoriesRequest{ParentId: 1041},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dis := &Distribution{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := dis.Categories(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("Categories() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestDistribution_SpuList(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisSpuListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisSpuListResponse
		wantErr bool
	}{
		{
			args: args{in: &sh.DisSpuListRequest{
				Page:        1,
				PageSize:    10,
				MinRate:     8.88,
				MaxRate:     8.88,
				GoodsName:   "YGL",
				SkuId:       "1023400001",
				SpuId:       "1023400",
				CategoryId:  1111,
				IsRecommend: 0,
			}},
		}, {
			args: args{in: &sh.DisSpuListRequest{Page: 2}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dis := &Distribution{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := dis.SpuList(tt.args.ctx, tt.args.in)

			t.Log(kit.JsonEncode(gotOut))

			if (err != nil) != tt.wantErr {
				t.Errorf("SpuList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestDistribution_SpuDetail(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisSpuDetailRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisSpuDetailResponse
		wantErr bool
	}{
		{
			args: args{in: &sh.DisSpuDetailRequest{SpuId: 1023400}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dis := &Distribution{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := dis.SpuDetail(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("SpuDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestDistribution_SpuUpdate(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisSpuUpdateRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
				in: &sh.DisSpuUpdateRequest{
					SpuId:       1023400,
					IsRecommend: &wrappers.BoolValue{Value: false},
					Sort:        &wrappers.Int32Value{Value: 100},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dis := &Distribution{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := dis.SpuUpdate(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil) != tt.wantErr {
				t.Errorf("SpuUpdate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestDistribution_SpuLogs(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisSpuLogsRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisSpuLogsResponse
		wantErr bool
	}{
		{
			args: args{in: &sh.DisSpuLogsRequest{
				SpuId:    1023400,
				Page:     0,
				PageSize: 0,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dis := &Distribution{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := dis.SpuLogs(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(gotOut))
			if (err != nil || gotOut.Code > 200) != tt.wantErr {
				t.Errorf("SpuLogs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestDistribution_SkuUpdate(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisSkuUpdateRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisResponse
		wantErr bool
	}{
		/*{
			args: args{in: &sh.DisSkuUpdateRequest{
				SkuId:                1023400001,
				Write:                &wrappers.StringValue{Value: "测试哈哈哈哈"},
				NormalCommissionRate: &wrappers.FloatValue{Value: 30},
				IsDis:                &wrappers.BoolValue{Value: true},
			}, ctx: context.Background()},
		},*/{
			args: args{in: &sh.DisSkuUpdateRequest{
				SkuId:                1023400001,
				NormalCommissionRate: &wrappers.FloatValue{Value: 10},
			}, ctx: context.Background()},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dis := &Distribution{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := dis.SkuUpdate(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("SkuUpdate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestDistribution_NotDisSkuList(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisNotDisSkuListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisNotDisSkuListResponse
		wantErr bool
	}{
		{
			args: args{in: &sh.DisNotDisSkuListRequest{
				SkuId:    "",
				SpuId:    "",
				Name:     "粮",
				Page:     0,
				PageSize: 0,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dis := &Distribution{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := dis.NotDisSkuList(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("NotDisSkuList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestDistribution_ImportList(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisImportListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisImportListResponse
		wantErr bool
	}{
		{
			args: args{
				in: &sh.DisImportListRequest{
					Page:     0,
					PageSize: 0,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dis := &Distribution{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := dis.ImportList(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("ImportList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
