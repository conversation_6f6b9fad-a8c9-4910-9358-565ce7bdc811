package services

import (
	"_/models"
	"_/utils"
	"context"
	"encoding/json"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"regexp"
	"strings"
	"time"
)

const (
	StoreID              = 1 // 电商门店id
	LimitActivityDoing   = 1 // 进行中
	LimitActivityUnStart = 2 // 未开始
	LimitActivityFinish  = 3 // 已结束
	LimitActivityStop    = 4 // 已失效
	MinuteLayout         = "2006-01-02 15:04"
)

var StatusWord = map[int32]string{
	LimitActivityDoing:   "已生效",
	LimitActivityUnStart: "未生效",
	LimitActivityFinish:  "已结束",
	LimitActivityStop:    "已失效",
}

type DistributionCommon struct {
	BaseService
}

// 防止重复提交
func (dc *DistributionCommon) isRepeatSubmit(in interface{}) bool {
	strIn, _ := json.Marshal(in)
	redis := ConnectRedis()
	defer redis.Close()
	return !redis.SetNX(kit.GetMd5(string(strIn)), 1, time.Second*3).Val()
}

// 获取状态值，整形转文字
func (dc *DistributionCommon) getStatusWord(status int32) string {
	if str, ok := StatusWord[status]; ok {
		return str
	}
	return ""
}

// 添加日志
func (dc *DistributionCommon) logAdd(ctx context.Context, logs models.UpetDisGoodsLog) (err error) {
	ctxInfo := utils.LoadGrpcContext(ctx)
	logs.UserName = ctxInfo.UserInfo.UserName
	logs.CreatedAt = time.Now()
	db := models.GetDBConn()
	if _, err = db.Insert(&logs); err != nil {
		glog.Error("限时佣金，日志添加失败，", logs, err.Error())
	}
	return
}

// 导入结果日志添加
func (dc *DistributionCommon) logImportAdd(ctx context.Context, logs models.UpetDisGoodsImport) (err error) {
	ctxInfo := utils.LoadGrpcContext(ctx)
	logs.UserName = ctxInfo.UserInfo.UserName
	logs.CreatedAt = time.Now()
	db := models.GetDBConn()
	if _, err = db.Insert(&logs); err != nil {
		glog.Error("限时佣金，导入日志添加失败，", logs, err.Error())
	}
	return
}

// 处理商品规格序列化值
func (dc *DistributionCommon) dealGoodsSpec(str string) (rStr string) {
	if len(str) < 1 {
		return ""
	}
	defer func() {
		if err := recover(); err != nil {
			glog.Error("规格字符串截取失败,", err)
		}
	}()

	reg := regexp.MustCompile(`"(.*?)"`)
	s := reg.FindAllString(str, -1)
	if len(s) > 0 {
		for _, v := range s {
			rStr = rStr + strings.Trim(v, `""`)
		}
	}
	return
}
