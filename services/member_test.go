package services

import (
	"_/proto/mm"
	"context"
	"reflect"
	"testing"
)

func TestMember_StoreMemberGoodBrowseInfo(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *mm.StoreMemberGoodBrowseInfoRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *mm.StoreMemberGoodBrowseInfoResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &mm.StoreMemberGoodBrowseInfoRequest{
					MemberId: "057556b0ccfb40f3a66a4f7f054fd6e9",
					OrgId:    2,
					SkuId:    112046,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := Member{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.StoreMemberGoodBrowseInfo(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("StoreMemberGoodBrowseInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("StoreMemberGoodBrowseInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMember_ClearMemberGoodBrowse(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *mm.ClearMemberGoodBrowseRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *mm.ClearMemberGoodBrowseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &mm.ClearMemberGoodBrowseRequest{
					MemberId: "12312",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := Member{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.ClearMemberGoodBrowse(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ClearMemberGoodBrowse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ClearMemberGoodBrowse() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMember_GetMemberGoodBrowseList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *mm.GetMemberGoodBrowseListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *mm.GetMemberGoodBrowseListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				ctx: context.Background(),
				in: &mm.GetMemberGoodBrowseListRequest{
					MemberId: "057556b0ccfb40f3a66a4f7f054fd6e9",
					OrgId:    2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := Member{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.GetMemberGoodBrowseList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMemberGoodBrowseList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetMemberGoodBrowseList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMember_MemberMergeSearchList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *mm.MemberMergeRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *mm.MemberMergeResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &mm.MemberMergeRequest{
					MemberPhone: "15118811943",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := Member{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.MemberMergeSearchList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("MemberMergeSearchList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MemberMergeSearchList() got = %v, want %v", got, tt.want)
			}
		})
	}
}
