package services

import (
	"_/proto/sh"
	"context"
	"testing"
)

func TestProduct_UserLevel(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *sh.UserLevelRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *sh.UserLevelResponse
		wantErr bool
	}{
		{
			args: args{
				in: &sh.UserLevelRequest{
					ScrmUserId: "697c53c494344f5eaa646b0e065f8914",
					WithEquity: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := Product{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.UserLevel(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.UserLevel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Code != 200 {
				t.<PERSON><PERSON>("Product.UserLevel() = %v, want %v", got, tt.want)
			}
		})
	}
}
