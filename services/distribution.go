package services

import (
	"_/helpers"
	"_/helpers/distribution"
	"_/models"
	"_/proto/sh"
	"bytes"
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes/wrappers"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"google.golang.org/grpc/metadata"
)

type Distribution struct {
	DistributionCommon
	DistributionActivity
}

// Categories 商品分类
func (dis *Distribution) Categories(ctx context.Context, in *sh.DisCategoriesRequest) (out *sh.DisCategoriesResponse, e error) {
	out = &sh.DisCategoriesResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("分销商品 Categories 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	err := db.Table("upet_goods_class").Alias("c").Select("gc_id as id,gc_name as name,"+
		"(select count(*) from upet_goods_class gc where gc.gc_parent_id = c.gc_id) as children_count").
		Where("gc_parent_id = ?", in.ParentId).
		OrderBy("gc_sort asc,gc_id asc").Find(&out.Data)

	if err != nil {
		out.Message = "查找分类出错 " + err.Error()
		return
	} else if len(out.Data) == 0 {
		out.Message = "上级分类id无效"
		return
	}

	out.Code = 200
	return
}

// SpuList 分销商品spu
func (dis *Distribution) SpuList(ctx context.Context, in *sh.DisSpuListRequest) (out *sh.DisSpuListResponse, e error) {
	out = &sh.DisSpuListResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("分销商品 SpuList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	query := db.Table("upet_goods_common").Alias("c").Select("c.goods_commonid as spu_id,c.goods_image as image_url,"+
		"c.goods_name as name,c.goods_price as price,from_unixtime(c.dis_add_time) as add_time,"+
		"c.distri_sort as is_recommend,c.distri_goods_sort as sort, c.gc_name as category").
		Join("inner", []string{"upet_goods", "g"}, "g.goods_commonid = c.goods_commonid").
		Where("c.is_dis = 1 and c.goods_state = 1 AND c.store_id=? AND g.store_id=?", in.OrgId, in.OrgId).
		OrderBy("c.distri_sort desc,if(c.distri_sort > 0,c.distri_goods_sort,9999) asc,c.dis_add_time desc")

	if in.MinRate > 0 {
		query.Where("g.dis_commis_rate >= ?", in.MinRate-0.001)
	}
	if in.MaxRate > 0 {
		query.Where("g.dis_commis_rate <= ?", in.MaxRate+0.001)
	}
	if len(in.GoodsName) > 0 {
		query.Where("c.goods_name like ?", "%"+in.GoodsName+"%")
	}
	if len(in.SkuId) > 0 {
		query.Where("g.goods_id = ?", cast.ToInt(in.SkuId))
	}
	if len(in.SpuId) > 0 {
		query.Where("c.goods_commonid = ?", cast.ToInt(in.SpuId))
	}
	if in.CategoryId > 0 {
		query.Where("c.gc_id_1 = ? or c.gc_id_2 = ? or c.gc_id_3 = ?", in.CategoryId, in.CategoryId, in.CategoryId)
	}
	if in.IsRecommend > 0 {
		query.Where("c.distri_sort = ?", in.IsRecommend-1)
	}

	pr := &helpers.PaginateReq{
		Count:    query.Clone().Select("count(DISTINCT c.goods_commonid)"),
		List:     query.Distinct(),
		Page:     in.Page,
		PageSize: in.PageSize,
	}
	if err := pr.Paginate(&out.Total, &out.Data); err != nil {
		out.Message = err.Error()
		return
	}

	for _, spu := range out.Data {
		spu.ImageUrl = helpers.GetGoodsListUrl(spu.ImageUrl)
		spu.Category = strings.ReplaceAll(spu.Category, " &gt;", ">")
	}
	out.Code = 200
	return
}

// SpuDetail 分销商品spu详情
func (dis *Distribution) SpuDetail(ctx context.Context, in *sh.DisSpuDetailRequest) (out *sh.DisSpuDetailResponse, e error) {
	out = &sh.DisSpuDetailResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("分销商品 SpuDetail 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	// 这里Get查询无法传pb结构体，用结构体中转麻烦，故用find中转一下
	spusTemp := make([]*sh.DisSpuListData, 0)
	db := models.GetDBConn()

	if err := db.Table("upet_goods_common").
		Select("goods_commonid as spu_id,goods_image as image_url,goods_name as name,goods_price as price,"+
			"from_unixtime(dis_add_time) as add_time,distri_sort as is_recommend,distri_goods_sort as sort,gc_name as category").
		Where("goods_commonid = ?", in.SpuId).
		Where("is_dis = 1 and goods_state = 1").Limit(1).Find(&spusTemp); err != nil {
		out.Message = "查询Spu出错 " + err.Error()
		return
	} else if len(spusTemp) == 0 {
		out.Message = "SpuId无效"
		return
	} else {
		out.Spu = spusTemp[0]
		out.Spu.Category = strings.ReplaceAll(out.Spu.Category, " &gt;", ">")
	}

	query := db.Table("upet_goods").Select("goods_id as sku_id,goods_image as image_url,goods_name as spec_name,"+
		"dis_commis_rate as commission_rate,dis_normal_commis_rate as normal_commission_rate,dis_write as `write`").
		Where("goods_commonid = ? and is_dis = 1 AND store_id=?", in.SpuId, in.OrgId)

	pr := &helpers.PaginateReq{
		Count:    query.Clone().Select("count(*)"),
		List:     query,
		Page:     in.Page,
		PageSize: in.PageSize,
	}
	if err := pr.Paginate(&out.Total, &out.Skus); err != nil {
		out.Message = err.Error()
		return
	}

	for _, sku := range out.Skus {
		sku.ImageUrl = helpers.GetGoodsListUrl(sku.ImageUrl)
		// 仅显示规格值
		sku.SpecName = strings.ReplaceAll(sku.SpecName, out.Spu.Name+" ", "")
		if sku.NormalCommissionRate == 0 {
			sku.NormalCommissionRate = sku.CommissionRate
		}
	}
	out.Code = 200
	return
}

// SpuUpdate spu更新（包括 推荐/取消推荐到首页、更新首页排序）
func (dis *Distribution) SpuUpdate(ctx context.Context, in *sh.DisSpuUpdateRequest) (out *sh.DisResponse, e error) {
	out = &sh.DisResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("分销商品 SpuUpdate 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	spu := &models.UpetGoodsCommon{}
	if has, err := db.Table("upet_goods_common").Select("distri_goods_sort,distri_sort").Where("goods_commonid = ?", in.SpuId).
		Where("is_dis = 1 and goods_state = 1").Get(spu); err != nil {
		out.Message = "查询出错 " + err.Error()
		return
	} else if !has {
		out.Message = "SpuId无效"
		return
	}

	var log []string
	update := make(map[string]interface{})
	// 推荐数据变更
	if in.IsRecommend != nil {
		if in.IsRecommend.Value {
			update["distri_sort"] = 1
			log = append(log, "推荐到首页")
		} else {
			update["distri_sort"] = 0
			log = append(log, "取消推荐到首页")
		}
	}
	// 排序数据变更
	if in.Sort != nil {
		update["distri_goods_sort"] = in.Sort.Value
		log = append(log, fmt.Sprintf("首页排序编辑 %v->%v", spu.DistriGoodsSort, in.Sort.Value))
	}
	if len(update) == 0 {
		out.Message = "没有有效更新数据"
		return
	}

	if _, err := db.Table("upet_goods_common").Where("goods_commonid = ?", in.SpuId).Update(update); err != nil {
		out.Message = "更新出错" + err.Error()
		return
	}
	// 插入操作日志
	_ = distribution.InsertLog(ctx, &models.UpetDisGoodsLog{
		Spu:     int(in.SpuId),
		Content: strings.Join(log, "，"),
	})

	out.Code = 200
	return
}

// SpuLogs spu操作日志
func (dis *Distribution) SpuLogs(ctx context.Context, in *sh.DisSpuLogsRequest) (out *sh.DisSpuLogsResponse, e error) {
	out = &sh.DisSpuLogsResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("分销商品 SpuLogs 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	query := db.Table("upet_dis_goods_log").Select("user_name,content,created_at").
		Where("spu = ?", in.SpuId).OrderBy("id desc")

	pr := &helpers.PaginateReq{
		Count:    query.Clone().Select("count(*)"),
		List:     query,
		Page:     in.Page,
		PageSize: in.PageSize,
	}
	if err := pr.Paginate(&out.Total, &out.Data); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// SkuUpdate sku更新（包括 添加分销、取消分销、推广文案变更、佣金变更）
func (dis *Distribution) SkuUpdate(ctx context.Context, in *sh.DisSkuUpdateRequest) (out *sh.DisResponse, e error) {
	out = &sh.DisResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("分销商品 SkuUpdate 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	sku := &models.UpetGoods{}
	session := models.GetDBConn().NewSession()
	defer session.Close()
	_ = session.Begin()

	if has, err := session.Table("upet_goods").Select("goods_id,dis_write,is_dis,dis_commis_rate,dis_activity_id,dis_normal_commis_rate,goods_commonid").
		Where("goods_id = ?", in.SkuId).Where("goods_state = 1 AND store_id=?", in.OrgId).Get(sku); err != nil {
		out.Message = "查询出错 " + err.Error()
		return
	} else if !has {
		out.Message = "SkuId无效"
		return
	}
	if sku.DisNormalCommisRate <= 0 {
		sku.DisNormalCommisRate = sku.DisCommisRate
	}

	var log []string
	update := make(map[string]interface{})

	// 编辑文案
	if in.Write != nil {
		log = append(log, fmt.Sprintf("编辑分销商品%v 文案 %s->%s", in.SkuId, sku.DisWrite, in.Write.Value))
		update["dis_write"] = in.Write.Value
	}

	// 分销状态变更优先处理
	if in.IsDis != nil {
		if msg, err := distribution.SkuUpdateStateByReq(session, sku, in); err != nil {
			out.Message = err.Error()
			return
		} else if len(msg) > 0 {
			log = append(log, msg)
		}
	} else if in.NormalCommissionRate != nil {
		if (in.NormalCommissionRate.Value-0.01) < -0.001 || (in.NormalCommissionRate.Value-50) > 0.001 {
			out.Message = "佣金比例最小为0.01，最大为50"
			return
		}
		update["dis_normal_commis_rate"] = in.NormalCommissionRate.Value
		// 非活动状态还需更新当前佣金
		if sku.DisActivityId == 0 {
			update["dis_commis_rate"] = in.NormalCommissionRate.Value
		}
		log = append(log, fmt.Sprintf("编辑分销商品%v 日常佣金 %v%%->%v%%", in.SkuId, sku.DisNormalCommisRate, in.NormalCommissionRate.Value))
	}

	if len(update) > 0 {
		if _, err := session.Table("upet_goods").Where("goods_id = ? AND store_id=?", in.SkuId, in.OrgId).Update(update); err != nil {
			out.Message = "更新出错" + err.Error()
			return
		}
	}
	if err := session.Commit(); err != nil {
		out.Message = "提交事务出错 " + err.Error()
		return
	}

	// 插入操作日志
	if len(log) > 0 {
		_ = distribution.InsertLog(ctx, &models.UpetDisGoodsLog{
			Spu:     sku.GoodsCommonid,
			Sku:     int(in.SkuId),
			Content: strings.Join(log, "，"),
		})
	}

	out.Code = 200
	return
}

// NotDisSkuList 非分销sku列表
func (dis *Distribution) NotDisSkuList(ctx context.Context, in *sh.DisNotDisSkuListRequest) (out *sh.DisNotDisSkuListResponse, e error) {
	out = &sh.DisNotDisSkuListResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("分销商品 NotDisSkuList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	query := db.Table("upet_goods").Select("goods_id as sku_id,goods_image as image_url,goods_name as name,goods_price as price").
		Where("is_dis = 0 and goods_state = 1 AND store_id=?", in.OrgId).OrderBy("goods_addtime desc")
	if len(in.SkuId) > 0 {
		query.Where("goods_id = ?", cast.ToInt(in.SkuId))
	}
	if len(in.SpuId) > 0 {
		query.Where("goods_commonid = ?", cast.ToInt(in.SpuId))
	}
	if len(in.Name) > 0 {
		query.Where("goods_name like ?", "%"+in.Name+"%")
	}

	pr := &helpers.PaginateReq{
		Count:    query.Clone().Select("count(*)"),
		List:     query,
		Page:     in.Page,
		PageSize: in.PageSize,
	}
	if err := pr.Paginate(&out.Total, &out.Data); err != nil {
		out.Message = err.Error()
		return
	}

	for _, sku := range out.Data {
		sku.ImageUrl = helpers.GetGoodsListUrl(sku.ImageUrl)
	}

	out.Code = 200
	return
}

// ImportTemplate 下载导入模板
func (dis *Distribution) ImportTemplate(ctx context.Context, in *sh.DisImportTemplateRequest) (out *sh.DisImportTemplateResponse, e error) {
	out = &sh.DisImportTemplateResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("分销商品 ImportTemplate 入参：", kit.JsonEncode(in), "，返回：", out.Code, out.Message)
		}
	}()

	f := excelize.NewFile()

	switch in.Type {
	case 0:
		fallthrough
	case 1:
		_ = f.SetCellValue("Sheet1", "A1", "SKU")
		_ = f.SetCellValue("Sheet1", "B1", "佣金（百分比数字部分，必填，如填0表示取消分销）")
		_ = f.SetCellValue("Sheet1", "C1", "推广文案（选填）")
	case 2:
		_ = f.SetCellValue("Sheet1", "A1", "商品ID(SKU)")
		_ = f.SetCellValue("Sheet1", "B1", "活动佣金(如1)")
	case 3:
		_ = f.SetCellValue("Sheet1", "A1", "SKU")
		_ = f.SetCellValue("Sheet1", "B1", "秒杀价格")
		_ = f.SetCellValue("Sheet1", "C1", "活动库存")
	default:
		out.Message = "参数Type无效"
		return
	}

	if buffer, err := f.WriteToBuffer(); err != nil {
		out.Message = err.Error()
		return
	} else {
		out.Template = buffer.Bytes()
	}
	out.Code = 200
	return
}

// Import 批量导入
func (dis *Distribution) Import(ctx context.Context, in *sh.DisImportRequest) (out *sh.DisResponse, e error) {
	out = &sh.DisResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("分销商品 Import 入参：", "，返回：", kit.JsonEncode(out))
		}
	}()

	redis := ConnectRedis()
	defer redis.Close()
	if in.Type != 3 {
		in.Type = 1
	}
	lockKey := fmt.Sprintf("distribution:import:%v", in.Type)
	if lock, err := redis.SetNX(lockKey, 1, time.Second*60).Result(); err != nil {
		out.Message = "获取锁失败 " + err.Error()
		return
	} else if !lock {
		out.Message = "有正在执行的导入任务，请稍后重试"
		return
	}
	defer redis.Del(lockKey)

	file, err := excelize.OpenReader(bytes.NewReader(in.File))
	if err != nil {
		out.Message = "读取文件出错 " + err.Error()
		return
	}
	defer file.Close()

	username := ""
	if md, success := metadata.FromIncomingContext(ctx); success {
		if len(md.Get("user-name")) > 0 {
			username = md.Get("user-name")[0]
		}
	}
	switch in.Type {
	case 3:
		if err = distribution.ImportSeckillProduct(username, file, in.Type, in.Id, in.OrgId); err != nil {
			out.Message = err.Error()
			return
		}
	default:
		if err = distribution.Import(username, file, in.Type, in.OrgId); err != nil {
			out.Message = err.Error()
			return
		}
	}
	out.Code = 200
	return
}

// ImportList 导入历史
func (dis *Distribution) ImportList(ctx context.Context, in *sh.DisImportListRequest) (out *sh.DisImportListResponse, e error) {
	out = &sh.DisImportListResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("分销商品 ImportList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	query := db.Table("upet_dis_goods_import").Select("id,created_at,result,result_url").OrderBy("id desc")

	switch in.Type {
	case 0:
		fallthrough
	case 1:
		query.Where("type = 1")
	case 2, 3:
		query.Where("type = ? and type_id = ?", in.Type, in.TypeId)
	default:
		out.Message = "参数Type无效"
		return
	}

	pr := &helpers.PaginateReq{
		Count:    query.Clone().Select("count(*)"),
		List:     query,
		Page:     in.Page,
		PageSize: in.PageSize,
	}
	if err := pr.Paginate(&out.Total, &out.Data); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// 分销设置全局默认佣金
func (dis *Distribution) DisSetGlobalCommission(ctx context.Context, in *sh.DisSetGlobalCommissionRequest) (out *sh.DisResponse, err error) {
	out = new(sh.DisResponse)
	db := models.GetDBConn()

	if _, err = db.Exec("UPDATE `upet_store_extend` SET dis_commis_rate = ? WHERE store_id = ? ", in.DefaultCommission, StoreID); err != nil {
		out.Message = "设置失败，请稍后再试," + err.Error()
		out.Code = 400
		return
	}

	out.Code = 200
	out.Message = "操作成功"
	return
}

// 分销获取全局默认佣金
func (dis *Distribution) DisGlobalCommission(ctx context.Context, in *sh.EmptyRequest) (out *sh.DisGlobalCommissionResponse, err error) {
	out = new(sh.DisGlobalCommissionResponse)

	db := models.GetDBConn()
	if _, err = db.SQL("SELECT dis_commis_rate FROM `upet_store_extend` WHERE store_id = ?", StoreID).Get(&out.DefaultCommission); err != nil {
		out.Message = "默认佣金获取失败，请稍后再试," + err.Error()
		out.Code = 400
	}

	out.Code = 200
	return
}

// 限时佣金/分销商品列表
func (dis *Distribution) DisGoodsList(ctx context.Context, in *sh.DisGoodsListRequest) (out *sh.DisGoodsListResponse, err error) {
	out = new(sh.DisGoodsListResponse)
	out.Code = 400

	db := models.GetDBConn()
	// A、搜索条件处理
	var condition strings.Builder
	condition.WriteString(fmt.Sprintf("WHERE is_dis = 1 AND goods_state = 1 AND store_id=%d ", in.OrgId))
	if len(in.SearchName) > 0 {
		switch in.SearchType {
		case 1:
			condition.WriteString(" AND goods_name like '%" + in.SearchName + "%'")
		case 2:
			condition.WriteString(" AND goods_id = " + cast.ToString(cast.ToInt(in.SearchName)))
		default:
			out.Message = "未知搜索类型"
			return
		}
	}
	if in.ActivityId > 0 { // 过滤已经添加的
		var skuIds []string
		if err = db.SQL("SELECT sku_id FROM  `upet_dis_commission_activity_goods` WHERE activity_id = ? AND status = ?", in.ActivityId, 1).Find(&skuIds); err != nil {
			out.Message = "活动商品查询失败，" + err.Error()
			return
		}
		if len(skuIds) > 0 {
			condition.WriteString(" AND goods_id NOT IN  (" + strings.Join(skuIds, ",") + ")")
		}
	}

	// B、分页数据查询
	sqlCount := "SELECT count(*) as total FROM `upet_goods` " + condition.String()
	condition.WriteString(" ORDER BY goods_id DESC limit " + cast.ToString(in.PageSize*in.Page-in.PageSize) + "," + cast.ToString(in.PageSize))
	sql := "SELECT store_id,(case when store_id = 4 then '百林康源' when store_id = 3 then '润合云店'  else '' end ) as shop_name, goods_id as sku_id,goods_name,goods_spec as spec_name,goods_image,goods_price,IF(dis_normal_commis_rate > 0,dis_normal_commis_rate,dis_commis_rate) dis_normal_commis_rate FROM `upet_goods` " + condition.String()
	if err = db.SQL(sql).Find(&out.Data); err != nil {
		out.Message = "查询失败，" + err.Error()
		return
	}
	if _, err = db.SQL(sqlCount).Get(&out.Total); err != nil {
		out.Message = "总条数查询失败，" + err.Error()
		return
	}
	// 商品规格处理
	if len(out.Data) > 0 {
		for k, _ := range out.Data {
			out.Data[k].SpecName = dis.dealGoodsSpec(out.Data[k].SpecName)
			out.Data[k].GoodsImage = helpers.GetGoodsListUrl(out.Data[k].GoodsImage)
		}
	}

	out.Code = 200
	out.Message = "查询成功！"
	return
}

// 导出非分销商品数据
func (dis *Distribution) ExportNoDisGoods(ctx context.Context, in *wrappers.Int32Value) (out *sh.DisImportDownloadResponse, error error) {
	out = new(sh.DisImportDownloadResponse)
	out.Code = 400
	db := models.GetDBConn()
	// A、数据查询
	var data []*models.UpetGoods
	if err := db.Select("goods_id,goods_commonid,goods_name,goods_price,goods_spec").Where("is_dis = ? AND goods_state = ? AND store_id=?", 0, 1, in.Value).Find(&data); err != nil {
		out.Message = err.Error()
		return
	}

	// B、excel文件流
	f := excelize.NewFile()
	writer, _ := f.NewStreamWriter("Sheet1")

	_ = writer.SetRow("A1", []interface{}{
		"sku", "spu", "商品名称", "规格", "价格",
	})

	for i, r := range data {
		// goods_spec序列化字符串处理
		goodsSpec := dis.dealGoodsSpec(r.GoodsSpec)
		_ = writer.SetRow("A"+cast.ToString(i+2), []interface{}{
			r.GoodsId, r.GoodsCommonid, r.GoodsName, goodsSpec, r.GoodsPrice,
		})
	}
	if err := writer.Flush(); err != nil {
		out.Message = err.Error()
		return
	}

	if buffer, err := f.WriteToBuffer(); err != nil {
		out.Message = err.Error()
		return
	} else {
		out.File = buffer.Bytes()
	}

	out.Code = 200
	out.Message = "查询成功！"
	return
}
