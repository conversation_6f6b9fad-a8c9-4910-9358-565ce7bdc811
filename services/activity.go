package services

import (
	"_/models"
	"_/proto/common"
	"_/proto/sh"
	"_/utils"
	"context"
	"encoding/json"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/spf13/cast"
	"sort"
	"time"
)

var (
	shareCountKey   = "activity920:share"
	redisExpiration = time.Hour * 24 * 30
)

type ActivityService struct {
}

// 幸运码存储
func (as *ActivityService) LuckyNumSave(ctx context.Context, in *sh.LuckyNumSaveRequest) (out *sh.LuckyNumSaveResponse, err error) {
	out = new(sh.LuckyNumSaveResponse)
	out.Code = 400
	out.IsDelLuckyNum = 1 // 返还幸运码
	db := models.GetDBConn()
	redisClient := common.GetRedisConn()
	defer redisClient.Close()

	// A-1、活动状态检测
	var lotteryDraw models.UpetLotteryDraw
	if _, err = db.Select("lottery_state").ID(in.LotteryId).Get(&lotteryDraw); err != nil {
		glog.Error("LuckyNumSave活动查询出错：", err.Error(), kit.JsonEncode(in))
		out.Message = "活动查询失败，稍后再试"
		out.Error = err.Error()
		return
	}
	if lotteryDraw.LotteryState == 0 {
		glog.Error("LuckyNumSave活动未开始：", kit.JsonEncode(in))
		out.Message = "活动未开始"
		return
	}
	if lotteryDraw.LotteryState == 2 {
		glog.Error("LuckyNumSave活动已结束：", kit.JsonEncode(in))
		out.Message = "活动已结束"
		return
	}
	// A-2、幸运码检测
	has, err := db.Where("lottery_id = ? and lottery_number = ?", in.LotteryId, in.LuckyNum).Exist(&models.UpetLotteryNumbers{})
	if err != nil {
		glog.Error("LuckyNumSave查询出错：", err.Error(), kit.JsonEncode(in))
		out.Message = "系统异常，稍后再试"
		out.Error = err.Error()
		return
	}
	if has {
		glog.Error("LuckyNumSave幸运码重复，请手动补发：", err, kit.JsonEncode(in))
		out.Message = "幸运码保存失败，请联系客服补发"
		out.IsDelLuckyNum = 0
		return
	}

	// B、获取用户信息
	var member models.UpetMember
	if _, err = db.Select("/*FORCE_MASTER*/ member_id,member_name").Where("scrm_user_id = ?", in.ScrmId).Get(&member); err != nil {
		glog.Error("LuckyNumSave用户查询失败：", err, kit.JsonEncode(in))
		out.Message = "用户查询失败，稍后再试"
		out.Error = err.Error()
		return
	}
	if member.MemberId == 0 {
		out.Message = "用户不存在"
		glog.Error("LuckyNumSave用户不存在：", kit.JsonEncode(in), kit.JsonEncode(member))
		// 针对新用户，创建接口是通过协程异步同步电商的，会导致此处查不到用户信息，间隔1s再重新查询一次
		time.Sleep(time.Second * 1)

		if _, err = db.Select("/*FORCE_MASTER*/ member_id,member_name").Where("scrm_user_id = ?", in.ScrmId).Get(&member); err != nil {
			glog.Error("LuckyNumSave用户再次查询失败：", err, kit.JsonEncode(in))
			out.Message = "用户再次查询失败，稍后再试"
			out.Error = err.Error()
			return
		}
		if member.MemberId == 0 {
			glog.Error("LuckyNumSave用户不存在，间隔1s：", kit.JsonEncode(in), kit.JsonEncode(member))
			return
		}
	}

	// C、幸运码限制
	// <1>分享链接或海报进入，活动那个期间最多100个
	// <2>下单消费邀请码，最多60个
	if in.Type == 3 && in.InviteScrmId != "" && in.InviteMemberId == 0 { // 链接分享用的是scrm_user_id，转成member_id
		if _, err = db.SQL("select member_id from upet_member where scrm_user_id = ?", in.InviteScrmId).Get(&in.InviteMemberId); err != nil {
			out.Message = "分享人查询失败，稍后再试！"
			out.Error = err.Error()
			glog.Error("LuckyNumSave通过InviteScrmId查询用户失败：", err, kit.JsonEncode(in))
			return
		}
	}
	if in.Type == 3 || in.Type == 4 {
		var total, memberId int64
		// 重复检测
		if in.Type == 3 {
			if in.InviteMemberId == member.MemberId {
				out.Message = "自己不能邀请自己"
				glog.Info("LuckyNumSave自己不能邀请自己：", kit.JsonEncode(in), member.MemberId)
				return
			}
			if has, _ := db.Where("lottery_id = ? and member_id = ? and member_invite_id = ?", in.LotteryId, member.MemberId, in.InviteMemberId).Exist(&models.UpetLotteryMembers{}); has {
				out.Message = "重复邀请"
				glog.Info("LuckyNumSave重复邀请：", kit.JsonEncode(in))
				return
			}
			memberId = in.InviteMemberId
			// 针对并发处理
			if shareCount, _ := redisClient.HIncrBy(shareCountKey, cast.ToString(in.InviteScrmId), 1).Result(); shareCount > 100 {
				out.Message = "超过幸运码邀请上限"
				glog.Error("LuckyNumSave已超过幸运码邀请上限：", shareCount, kit.JsonEncode(in))
				return
			}
			redisClient.Expire(shareCountKey, redisExpiration)
		} else if in.Type == 4 {
			if has, _ := db.Where("lottery_id = ? and member_id = ? and order_sn = ?", in.LotteryId, member.MemberId, in.OrderSn).Exist(&models.UpetLotteryNumbers{}); has {
				out.Message = "此订单号已赠送过幸运码"
				glog.Info("LuckyNumSave此订单号已赠送过幸运码：", kit.JsonEncode(in))
				return
			}
			memberId = member.MemberId
		}
		_, err = db.SQL("SELECT count(*) as total FROM upet_lottery_numbers WHERE lottery_id = ? and member_id = ? and state = ?", in.LotteryId, memberId, in.Type).
			GroupBy("state").Get(&total)
		if err != nil {
			out.Message = "系统出错，稍后再试！"
			out.Error = err.Error()
			glog.Error("LuckyNumSave统计幸运码出错：", err, kit.JsonEncode(in))
			return
		}
		if (in.Type == 3 && total >= 100) || (in.Type == 4 && total >= 60) {
			out.Message = "已超过幸运码获取上限"
			glog.Error("LuckyNumSave已超过幸运码获取上限：", total, kit.JsonEncode(in))
			return
		}
	}

	// D、存储
	insertData := models.UpetLotteryNumbers{
		LotteryNumber: in.LuckyNum,
		LotteryId:     cast.ToInt64(in.LotteryId),
		State:         in.Type,
		AddTime:       time.Now().Unix(),
		OrderSn:       in.OrderSn,
	}
	if in.Type == 3 { // 邀请人奖励幸运码
		var inviteMember models.UpetMember
		if _, error := db.Select("member_id,member_name").Where("member_id = ?", in.InviteMemberId).Get(&inviteMember); error != nil {
			glog.Error("LuckyNumSave查询邀请人信息出错：", error, kit.JsonEncode(in))
		}
		insertData.MemberId = in.InviteMemberId
		insertData.MemberName = inviteMember.MemberName
		insertData.InviteMemberId = member.MemberId
		insertData.InviteMemberName = member.MemberName
	} else {
		insertData.MemberId = member.MemberId
		insertData.MemberName = member.MemberName
	}

	if _, err = db.Insert(&insertData); err != nil {
		redisClient.HIncrBy(shareCountKey, cast.ToString(in.InviteScrmId), -1)
		out.Message = "保存失败，稍后再试"
		out.Error = err.Error()
		glog.Error("LuckyNumSave保存失败：", err.Error(), kit.JsonEncode(in))
		return
	}
	// 邀请记录
	if in.Type == 3 {
		if _, err := db.Insert(&models.UpetLotteryMembers{
			LotteryId:      in.LotteryId,
			MemberId:       member.MemberId,
			MemberInviteId: in.InviteMemberId,
			AddTime:        time.Now().Unix(),
		}); err != nil {
			glog.Error("LuckyNumSave邀请记录保存失败：", err.Error(), kit.JsonEncode(in))
		}
	}

	out.IsDelLuckyNum = 0
	out.Code = 200
	out.Message = "success"
	return
}

// 我的幸运码
func (as *ActivityService) MyLuckyNumGet(ctx context.Context, in *sh.MyLuckyNumGetRequest) (out *sh.MyLuckyNumGetResponse, err error) {
	out = new(sh.MyLuckyNumGetResponse)
	out.Code = 400
	db := models.GetDBConn()

	var memberId int
	if _, err = db.SQL("select member_id from upet_member where scrm_user_id = ?", in.ScrmId).Get(&memberId); err != nil {
		glog.Info("MyLuckyNumGet我的幸运码，查询用户信息失败：", err, in)
		out.Error = err.Error()
		out.Message = "用户信息查询失败，稍后再试"
		return
	}
	if memberId == 0 {
		out.Message = "用户不存在"
		return
	}

	var lotteryNumber []*models.UpetLotteryNumbers
	err = db.Where("member_id = ? and lottery_id = ?", memberId, in.LotteryId).OrderBy("id desc").Find(&lotteryNumber)
	if err != nil {
		glog.Info("MyLuckyNumGet我的幸运码查询失败：", err, in)
		out.Error = err.Error()
		out.Message = "系统异常，稍后再试！"
		return
	}

	if len(lotteryNumber) > 0 {
		for _, ln := range lotteryNumber {
			out.Data = append(out.Data, &sh.MyLuckyNumGetData{
				Date:     time.Unix(ln.AddTime, 0).Format("01.02"),
				TypeName: models.GetLotteryNumbersStateName(ln.State),
				LuckyNum: ln.LotteryNumber,
			})
		}
	}

	out.Code = 200
	out.Message = "success"
	return
}

// 所有中奖信息
func (as *ActivityService) LuckyOpenInfoAll(ctx context.Context, in *sh.LuckyOpenInfoAllRequest) (out *sh.LuckyOpenInfoAllResponse, err error) {
	out = new(sh.LuckyOpenInfoAllResponse)
	out.Code = 400
	db := models.GetDBConn()

	var rewards []models.UpetLotteryReward
	if err = db.Where("lottery_id = ?", in.LotteryId).OrderBy("id desc").Find(&rewards); err != nil {
		glog.Info("LuckyOpenInfoAll获取中奖信息失败：", err, in)
		out.Message = "系统异常，稍后再试"
		out.Error = err.Error()
		return
	}

	if len(rewards) > 0 {
		var openTimeInfo []models.UpetLotteryOpenTime
		if err = db.Select("id,open_time").Find(&openTimeInfo); err != nil {
			glog.Info("LuckyOpenInfoAll获取开奖日期数据失败：", err, in)
			out.Message = "系统异常，稍后再试！"
			out.Error = err.Error()
			return
		}
		info := make(map[int]string)
		for k, _ := range openTimeInfo {
			info[openTimeInfo[k].Id] = openTimeInfo[k].OpenTime.Format("1月2日")
		}

		// 返回数据格组装、格式化
		rewardInfo := make(map[int][]*sh.LuckyOpenInfoAllInfoData)
		keys := make([]int, 0)
		for k, _ := range rewards {
			if _, has := rewardInfo[rewards[k].OpenTimeId]; !has {
				keys = append(keys, rewards[k].OpenTimeId)
			}
			rewardInfo[rewards[k].OpenTimeId] = append(rewardInfo[rewards[k].OpenTimeId], &sh.LuckyOpenInfoAllInfoData{
				LuckyNum: rewards[k].LotteryNumber,
				Mobile:   utils.HiddenMobile(rewards[k].Mobile),
				Avatar:   rewards[k].Avatar,
				Reward:   rewards[k].RewardId,
			})
		}
		sort.Ints(keys)
		for _, key := range keys {
			date, _ := info[key]
			out.Data = append(out.Data, &sh.LuckyOpenInfoAllData{
				Title:      date + "中奖信息",
				RewardInfo: rewardInfo[key],
			})
		}
	}

	out.Code = 200
	out.Message = "success"
	return
}

// 中奖信息
func (as *ActivityService) LuckyOpenInfo(ctx context.Context, in *sh.LuckyOpenInfoRequest) (out *sh.LuckyOpenInfoResponse, err error) {
	out = new(sh.LuckyOpenInfoResponse)
	out.RewardWay = new(sh.RewardWayInfo)
	out.Code = 400

	db := models.GetDBConn()
	// 1、获取所有开奖日期和状态
	var openTimes []*models.UpetLotteryOpenTime
	if err = db.OrderBy("open_time asc").Find(&openTimes); err != nil {
		glog.Info("LuckyOpenInfo查询开奖时间出错：", err, in)
		out.Message = "系统异常，稍后再试"
		out.Error = err.Error()
		return
	}
	if len(openTimes) < 1 {
		out.Message = "未配置开奖日期，请联系客服"
	}
	var statusName string
	var latestOpenTime *models.UpetLotteryOpenTime // 找出最新一期开奖的活动
	for _, v := range openTimes {
		if v.Status == 1 {
			statusName = "已开奖"
			if latestOpenTime == nil {
				latestOpenTime = v
			} else if v.OpenTime.Format("20060102") > latestOpenTime.OpenTime.Format("20060102") {
				latestOpenTime = v
			}
		} else {
			statusName = fmt.Sprintf("%d点开奖", v.OpenTime.Hour())
		}
		out.DateInfo = append(out.DateInfo, &sh.OpenInfoDate{
			Status:     v.Status,
			StatusName: statusName,
			Date:       v.OpenTime.Format("1.2"),
		})
	}

	// 2、获取最新一期开奖时间的中奖信息
	out.Title = "暂未开奖"
	if latestOpenTime != nil && latestOpenTime.Id > 0 {
		out.Title = latestOpenTime.OpenTime.Format("1月2日") + "开奖信息"
		err = db.SQL("select lottery_number as lucky_num,mobile,avatar,reward_id as reward from upet_lottery_reward where open_time_id = ? and lottery_id = ?", latestOpenTime.Id, in.LotteryId).OrderBy("reward_id desc").Find(&out.RewardInfo)
		if err != nil {
			glog.Info("LuckyOpenInfo获取中奖信息失败：", err, in)
			out.Message = "系统异常，稍后再试！"
			out.Error = err.Error()
			return
		}
		if len(out.RewardInfo) > 0 {
			for k, _ := range out.RewardInfo {
				out.RewardInfo[k].Mobile = utils.HiddenMobile(out.RewardInfo[k].Mobile)
			}
		}
	}

	// 3、查询当前用户是否中奖，只要有一期中奖品或免单，要显示"提交收货地址"或"加客服二维码"
	if in.ScrmId != "" {
		var member models.UpetMember
		if _, err = db.Select("member_id").Where("scrm_user_id = ?", in.ScrmId).Get(&member); err != nil {
			glog.Info("LuckyOpenInfo获取用户信息失败：", err, in)
		} else if member.MemberId > 0 {
			var rewards []models.UpetLotteryReward
			if err = db.Where("member_id = ? ", member.MemberId).Find(&rewards); err != nil {
				glog.Info("LuckyOpenInfo获取中奖记录失败：", err, member.MemberId)
			} else if len(rewards) > 0 {
				for k, _ := range rewards {
					// 免单
					if rewards[k].RewardId == 1 && out.RewardWay.IsFree == 0 {
						out.RewardWay.IsFree = 1
						out.RewardWay.QrCodeUrl = config.GetString("activity92_service_qr_code")
					}
					// 盲盒奖品
					if rewards[k].RewardId == 2 && out.RewardWay.IsShowAddress == 0 {
						out.RewardWay.IsShowAddress = 1
						var rewardAddress models.UpetLotteryRewardAddress
						if _, error := db.Select("id,lottery_id,name,city,address,mobile,adcode,tx_lat,tx_lng,house_info").Where("lottery_id = ? AND scrm_user_id = ?", in.LotteryId, in.ScrmId).Get(&rewardAddress); error != nil {
							glog.Error("LuckyOpenInfo获取用户中奖地址出错：", error, in)
						} else if rewardAddress.Id > 0 {
							addressBytes, _ := json.Marshal(rewardAddress)
							json.Unmarshal(addressBytes, &out.RewardWay.AddressInfo)
						}
					}
				}
			}
		}
	}

	out.Code = 200
	out.Message = "success"
	return
}

// 中奖地址提交
func (as *ActivityService) RewardAddress(ctx context.Context, in *sh.RewardAddressRequest) (out *sh.RewardAddressResponse, err error) {
	out = new(sh.RewardAddressResponse)
	out.Code = 400

	db := models.GetDBConn()
	var address models.UpetLotteryRewardAddress
	if _, err = db.Where("scrm_user_id = ? and lottery_id = ?", in.ScrmUserId, in.LotteryId).Get(&address); err != nil {
		out.Message = "系统异常，稍后再试"
		out.Error = err.Error()
		glog.Error("RewardAddress查询地址出错：", err, kit.JsonEncode(in))
		return
	}

	address.Name = in.Name
	address.City = in.City
	address.Address = in.Address
	address.Mobile = in.Mobile
	address.Adcode = in.Adcode
	address.TxLat = cast.ToFloat64(in.TxLat)
	address.TxLng = cast.ToFloat64(in.TxLng)
	address.HouseInfo = in.HouseInfo
	address.LotteryId = in.LotteryId
	address.ScrmUserId = in.ScrmUserId
	if address.Id > 0 {
		address.UpdateTime = time.Now()
		_, err = db.ID(address.Id).Update(&address)
	} else {
		address.AddTime = time.Now()
		_, err = db.Insert(&address)
	}
	if err != nil {
		out.Message = "地址提交失败，稍后再试！"
		out.Error = err.Error()
		glog.Error("RewardAddress地址提交失败：", err, kit.JsonEncode(address))
		return
	}

	out.Code = 200
	out.Message = "success"
	return
}
