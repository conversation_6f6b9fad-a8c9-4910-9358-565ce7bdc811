package services

import (
	"_/proto/sh"
	"context"
	"encoding/json"
	"reflect"
	"testing"
)

func TestMember_RecoverUserVoucher(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *sh.RecoverUserVoucherRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *sh.Response
		wantErr bool
	}{
		{
			args: args{
				in: &sh.RecoverUserVoucherRequest{
					UserVoucher: []*sh.RecoverUserVoucher{
						{ScrmUserId: "c0fcb4dc56a84d4fb9e1d00cd72f905e", VoucherTIds: []int64{112, 329}},
					},
				},
			},
			want:    &sh.Response{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := Voucher{}
			got, err := p.RecoverUserVoucher(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.<PERSON>("Member.InvalidateUserVoucher() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Member.InvalidateUserVoucher() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMember_FindUsedVoucherUserMobile(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *sh.FindUsedVoucherUserMobileRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *sh.FindUsedVoucherUserMobileResponse
		wantErr bool
	}{
		{
			args: args{
				in: &sh.FindUsedVoucherUserMobileRequest{
					VoucherTIds: []int64{87, 145},
				},
			},
			want:    &sh.FindUsedVoucherUserMobileResponse{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := Voucher{}
			got, err := p.FindUsedVoucherUserMobile(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Member.FindUsedVoucherUserMobile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Code != tt.want.Code {
				t.Errorf("Member.FindUsedVoucherUserMobile() = %v, want %v", got, tt.want)
			} else {
				b, err := json.Marshal(got)
				t.Log(111, err, string(b))
			}
		})
	}
}
