package services

import (
	"_/models"
	"_/proto/es"
	proto_models "_/proto/models"
	"_/proto/sh"
	"_/utils"
	"context"
	"encoding/json"
	"fmt"
	"github.com/olivere/elastic/v7"
	"strings"
	"time"

	"github.com/limitedlee/microservice/common/config"
	kit "github.com/tricobbler/rp-kit"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
)

type Product struct {
	BaseService
}

func (p Product) SkuRelList(ctx context.Context, in *sh.SkuRelRequest) (*sh.SkuRelResponse, error) {
	out := sh.SkuRelResponse{Code: 400}
	session := p.NewGetConn()
	defer session.Close()
	var list []models.SkuRel
	if err := session.Find(&list); err != nil {
		//glog.Error(err.Error())
	} else {
		out.Code = 200
		for _, v := range list {
			data := sh.SkuRelList{Id: v.Id, Name: v.Name, CreateTime: v.CreateTime, UpdateTime: v.UpdateTime}
			out.Data = append(out.Data, &data)
		}
	}

	return &out, nil
}

func (p Product) GoodsRelevanceList(ctx context.Context, in *sh.SkuRelListRequest) (*sh.SkuRelListResponse, error) {
	out := sh.SkuRelListResponse{Code: 400}
	session := p.NewGetConn()
	defer session.Close()
	var list []models.UpetGoodsRelevance

	where := ""
	if in.KeywordType == 1 && in.Keyword != "" {
		where = "relevance_name = ?"
	} else if in.KeywordType == 2 && in.Keyword != "" {
		where = "relevance_id = ?"
	}
	ps := cast.ToInt(in.PageSize)
	if ps <= 0 {
		ps = 10
	}
	page := cast.ToInt(in.PageIndex)
	if page <= 0 {
		page = 1
	}
	totalCount, _ := session.Table("upet_goods_relevance").Where(where, in.Keyword).And("store_id = ?", in.StoreId).Count()
	if err := session.Where(where, in.Keyword).And("store_id = ?", in.StoreId).Limit(ps, (page-1)*ps).OrderBy("relevance_id desc").Find(&list); err != nil {
	} else {
		out.Code = 200
		timeLayout := "2006-01-02 15:04:05" //转化所需模板
		for _, v := range list {
			addStr := time.Unix(cast.ToInt64(v.RelevanceAddtime), 0).Format(timeLayout)
			editStr := time.Unix(cast.ToInt64(v.RelevanceEdittime), 0).Format(timeLayout)
			data := sh.GoodsRelevancesList{Id: v.RelevanceId, Name: v.RelevanceName, Desc: v.RelevanceSpecifications, Addtime: addStr, Edittime: editStr}
			out.Data = append(out.Data, &data)
		}
		out.TotalCount = cast.ToInt32(totalCount)

	}

	return &out, nil
}

func (p Product) NewGoodsRelevance(ctx context.Context, in *sh.GoodsRelevanceRequest) (*sh.BaseResponse, error) {
	out := sh.BaseResponse{Code: 400}
	session := p.NewGetConn()
	defer session.Close()
	var list models.UpetGoodsRelevance
	list.RelevanceName = in.Name
	list.RelevanceSpecifications = in.Desc
	list.RelevanceVideo = in.Video
	list.RelevanceImg = in.Imgs

	if in.Id > 0 {
		list.RelevanceEdittime = cast.ToInt32(time.Now().Unix())
		if _, err := session.Where("relevance_id=?", in.Id).Update(&list); err != nil {
			//glog.Error(err.Error())
			fmt.Println(err.Error())
		} else {
			out.Code = 200
		}
	} else {
		list.RelevanceAddtime = cast.ToInt32(time.Now().Unix())
		list.RelevanceEdittime = cast.ToInt32(time.Now().Unix())
		a, _ := json.Marshal(list)
		fmt.Println(string(a))
		if _, err := session.Insert(&list); err != nil {

			fmt.Println(err.Error())
		} else {
			relevance_id := list.RelevanceId
			subdatas := []models.UpetGoodsRelevanceSku{}

			for _, vv := range in.GoodsSkus {
				subdata := models.UpetGoodsRelevanceSku{
					RelevanceId:             relevance_id,
					RelevanceName:           list.RelevanceName,
					RelevanceSpecifications: list.RelevanceSpecifications,
					GoodsId:                 vv.GoodsId,
					GoodsName:               "",
					GoodsCommonid:           0,
					GoodsSpec:               "",
					RelevanceSkuSort:        0,
					RelevanceSkuAddtime:     cast.ToInt32(time.Now().Unix()),
					RelevanceSkuEdittime:    cast.ToInt32(time.Now().Unix()),
				}

				subdatas = append(subdatas, subdata)
			}

			_, err := session.Insert(&subdatas)
			if err != nil {
				out.Message = err.Error()
				out.Error = err.Error()
				return &out, nil
			}
			fmt.Println(relevance_id)
			out.Code = 200
		}
	}

	return &out, nil
}

func (p Product) DeleteGoodsRelevance(ctx context.Context, in *sh.GoodsRelevanceDeleteRequest) (*sh.BaseResponse, error) {
	out := sh.BaseResponse{Code: 400}
	session := p.NewGetConn()
	defer session.Close()
	var list models.UpetGoodsRelevance
	ids := strings.Split(in.Id, ",")
	if _, err := session.In("relevance_id", ids).Delete(&list); err != nil {
		//glog.Error(err.Error())
		fmt.Println(err.Error())
	} else {

		out.Code = 200
	}

	return &out, nil
}

func (p Product) DeleteGoodsRelevanceSku(ctx context.Context, in *sh.GoodsRevanceSkuRequest) (*sh.BaseResponse, error) {
	out := sh.BaseResponse{Code: 400}
	session := p.NewGetConn()
	defer session.Close()
	var list models.UpetGoodsRelevanceSku
	if _, err := session.Where("relevance_sku_id=?", in.Id).Delete(&list); err != nil {
		//glog.Error(err.Error())
		fmt.Println(err.Error())
	} else {
		out.Code = 200
	}

	return &out, nil
}

// UpdateGoodsToEs 更新商品到ES
func (p Product) UpdateGoodsToEs(ctx context.Context, in *sh.GoodsToEsUpdateRequest) (out *sh.BaseResponse, e error) {
	out = &sh.BaseResponse{Code: 400}

	defer func() {
		glog.Info("UpdateGoodsToEs 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
	}()

	in.Ids = strings.Trim(in.Ids, ",")
	ids := strings.Split(in.Ids, ",")
	if len(ids) == 0 || len(in.Ids) == 0 {
		out.Code = 200
		return
	}
	db := models.GetDBConn()

	IndexChannelStoreProduct := "channel_store_product"
	//电商机构ID默认1
	org_id := 1
	if in.DsOrgId != 0 {
		org_id = cast.ToInt(in.DsOrgId)
	} else {
		//本地生活的机构ID要转化成电商的ID
		org := ""
		_, err := db.SQL("select ds_org_id from datacenter.organization_info where id=?", in.AwOrgId).Get(&org)
		if err != nil {
			glog.Error("UpdateGoodsToEs 查询主体ID失败：", err)
		}
		if org != "" {
			org_id = cast.ToInt(org)
		} else {
			return
		}
	}
	// 机构ID-门店ID
	var FinanceCode = "000000"
	if in.ShopId > 0 {
		FinanceCode = fmt.Sprintf("%d-%d", in.DsOrgId, in.ShopId)
	}

	//如果不是瑞鹏的，那么就用索引名称加上org_id 作为索引
	if org_id != 1 && org_id != 0 {
		IndexChannelStoreProduct = IndexChannelStoreProduct + cast.ToString(org_id)
	}

	// 先统一更新一波有无库存
	//生产配置DSStock: 0,6-保障卡虚拟仓,1294-电商仓,1638-巨星药品仓
	//uat配置DSStock: 0,1294,1638
	//如果相关的org_id跟in.ids的加密成key缓存redis 5分钟,如果key存在则不再更新
	redis := ConnectRedis()
	defer redis.Close()
	var MD5Key string
	MD5Key = utils.MD5(fmt.Sprintf("%s_%d", in.Ids, org_id))
	cmd := redis.Get(MD5Key)
	if cmd.Val() == "" {
		//更新库存
		if _, err := db.Exec(fmt.Sprintf(`update upet_goods g
    join (select g.goods_id, g.has_stock, if(sum(stock) > 0, 1, 0) current_has_stock
          from upet_goods g
                   left join dc_order.warehouse_goods wg
                             on concat(g.goods_id, '') = wg.goodsid and warehouse_id in (%s)
          where g.is_virtual = 0 and g.goods_type = 0 and g.goods_id in (%s) and g.store_id=%d
          group by g.goods_id
          having has_stock != current_has_stock) t on t.goods_id = g.goods_id
set g.has_stock = t.current_has_stock WHERE store_id=%d`, config.GetString("DSStock"), in.Ids, org_id, org_id)); err != nil {
			out.Message = err.Error()
			return
		}
		redis.Set(MD5Key, 1, time.Minute*5)
	}

	var goods []proto_models.UpetGoodsExt
	var fields = `g.goods_id,g.goods_commonid,g.goods_name,g.short_name,g.goods_jingle,g.gc_id,g.goods_price,g.goods_promotion_price,g.store_id,
	g.goods_promotion_type,g.spec_name,g.goods_spec,g.goods_salenum,g.goods_image,g.goods_state,g.is_virtual,g.g_search_status,g.goods_type,g.vip_state,
	g.member_price_1,g.goods_edittime,g.has_stock,g.is_open_virtual_stock,c.region_id,gc.gc_name as channel_category_name`
	session := db.Table("upet_goods").Alias("g")
	session.Join("inner", "upet_goods_common c", "c.goods_commonid = g.goods_commonid")
	session.Join("left", "upet_goods_class gc", "c.gc_id = gc.gc_id")
	session.Where("g.store_id=? and c.store_id=?", org_id, org_id)
	session.In("g.goods_id", ids)
	if org_id == 3 && in.ShopId > 0 { //连表查询upet_goods_eshop表
		session.Join("left", "upet_goods_eshop eshop", "g.goods_id = eshop.goods_id and eshop.shop_id = ?", in.ShopId)
		session.Where("eshop.store_id = ?", org_id)
	} else if org_id == 3 && in.ShopId == 0 {
		fields += `,group_concat(eshop.shop_id) as shop_ids`
		session.Join("left", "upet_goods_eshop eshop", "g.goods_id = eshop.goods_id")
		session.Where("eshop.store_id = ?", org_id).GroupBy("g.goods_id")
	}
	session.Select(fields)
	if err := session.Find(&goods); err != nil {
		out.Message = err.Error()
		return
	}

	activityGoods := make(map[string]*sh.GoodsActivity)
	for _, v := range in.GoodsDate {
		activityGoods[v.SkuId] = v
	}

	client := es.NewEsClient()
	defer client.Stop()

	// 有效的商品
	goodsMap := make(map[int32]proto_models.UpetGoodsExt)
	var goodsIds, vrIds []int32

	for _, good := range goods {
		// GSearchStatus 是否是允许搜索商品 1否，0是
		// 虚拟商品、组合商品、开启虚拟库存、有库存 的商品展示
		if good.GoodsState == 1 && !good.GSearchStatus &&
			(good.IsVirtual > 0 || good.GoodsType != 0 || good.IsOpenVirtualStock > 0 || good.HasStock > 0) {
			goodsMap[good.GoodsId] = good
			goodsIds = append(goodsIds, good.GoodsId)
			// 所有非全国虚拟商品，查适用门店城市
			if good.IsVirtual == 1 && good.RegionId > 0 {
				vrIds = append(vrIds, good.GoodsId)
			}
		}
	}

	// 存在有效的商品
	if len(goodsIds) > 0 {
		// 一次性查出商品标签
		var tags []*models.ProductTag
		tagsMap := make(map[int32]*models.ProductTag)
		if err := db.Table("dc_product.product_tag").In("sku_id", goodsIds).In("product_type", 1, 2).Find(&tags); err != nil {
			out.Message = err.Error()
			return
		}
		for _, tag := range tags {
			tagsMap[int32(tag.SkuId)] = tag
		}

		type GoodsCity struct {
			GoodsId int32
			City    string
		}
		var gcs []*GoodsCity
		gcMap := make(map[int32][]string)

		if len(vrIds) > 0 {
			// 一次性查适用城市
			if err := db.Table("upet_chain_stock").Alias("s").Join("inner", "upet_chain c", "c.chain_id = s.chain_id").
				Select("distinct s.goods_id,SUBSTRING_INDEX(c.area_info,' ',2) as city").In("s.goods_id", vrIds).
				Where("s.chain_goods_state = 1 and c.area_info != ''").Find(&gcs); err != nil {
				out.Message = err.Error()
				return
			}
			for _, gc := range gcs {
				area := strings.Split(gc.City, " ")
				if len(area) < 2 {
					continue
				}
				gcMap[gc.GoodsId] = append(gcMap[gc.GoodsId], area[1])
			}
		}
		// 构造批量创建请求体
		bulkRequest := client.Bulk()
		count := 0
		for _, good := range goodsMap {
			if tag, ok := tagsMap[good.GoodsId]; ok {
				good.Tags = tag.ToTagsString()
			}
			if cs, ok := gcMap[good.GoodsId]; ok && good.IsVirtual == 1 {
				good.Cities = cs
			}
			m := BulkBySkuId(FinanceCode, activityGoods, good)
			bulkRequest.Add(elastic.NewBulkIndexRequest().Index(IndexChannelStoreProduct).Id(fmt.Sprintf("%s-5-%d", FinanceCode, good.GoodsId)).Doc(m))
			//润合云店主店操作时：先批量删除，再批量添加
			if good.StoreId == 3 && good.ShopIds != "" && in.ShopId == 0 {
				query := elastic.NewTermQuery("sku_info.sku_id", cast.ToInt64(good.GoodsId))
				//根据code去删除数据，因为怕他们的数据有重复，比如删除后又创建个一样的财务编码的
				if _, err := client.DeleteByQuery().Index(IndexChannelStoreProduct).Query(query).Do(context.Background()); err != nil {
					glog.Error(fmt.Sprintf("UpdateGoodsToEs 批量删除出现异常 %s", err.Error()))
				}

				//批量添加
				//润合云店判断多个门店，再批量添加
				shopIdSplice := strings.Split(good.ShopIds, ",")
				for _, ShopId := range shopIdSplice {
					FinanceCode = fmt.Sprintf("%d-%s", good.StoreId, ShopId)
					m = BulkBySkuId(FinanceCode, activityGoods, good)
					bulkRequest.Add(elastic.NewBulkIndexRequest().Index(IndexChannelStoreProduct).Id(fmt.Sprintf("%s-5-%d", FinanceCode, good.GoodsId)).Doc(m))
					count++
					if count >= 1000 {
						count = 0
						bulkResponse, err := bulkRequest.Do(context.Background())
						if err != nil {
							glog.Error(fmt.Sprintf("UpdateGoodsToEs 批量更新出现异常 %s", err.Error()))
							return
						}
						// 检查是否有错误
						if bulkResponse.Errors {
							glog.Error(fmt.Sprintf("UpdateGoodsToEs 批量更新出现错误 %v", bulkResponse.Errors))
							return
						}
						bulkRequest = client.Bulk()
					}
				}
			}
		}
		// 执行 BulkRequest
		bulkResponse, err := bulkRequest.Do(context.Background())
		if err != nil {
			glog.Error(fmt.Sprintf("UpdateGoodsToEs 批量更新出现异常 %s", err.Error()))
			return
		}
		// 检查是否有错误
		if bulkResponse.Errors {
			glog.Error(fmt.Sprintf("UpdateGoodsToEs 批量更新出现错误 %v", bulkResponse.Errors))
			return
		}
	}
	// 批量删除无效的商品 单个指令删除 DELETE /channel_store_product3/_doc/3-57-1051430001-1051430001
	bulkRequest := client.Bulk()

	var flag = false
	for _, id := range ids {
		if org_id == 3 && len(goodsIds) == 0 && in.ShopId == 0 {
			query := elastic.NewTermQuery("sku_info.sku_id", cast.ToInt64(id))
			//根据code去删除数据，因为怕他们的数据有重复，比如删除后又创建个一样的财务编码的
			if _, err := client.DeleteByQuery().Index(IndexChannelStoreProduct).Query(query).Do(context.Background()); err != nil {
				glog.Error(fmt.Sprintf("UpdateGoodsToEs 批量删除出现异常 %s", err.Error()))
			}
		} else {
			// 是有效商品
			if _, ok := goodsMap[cast.ToInt32(id)]; ok {
				continue
			}
			deleteId := fmt.Sprintf("%s-5-%s", FinanceCode, id)
			flag = true
			bulkRequest.Add(elastic.NewBulkDeleteRequest().Index(IndexChannelStoreProduct).Id(deleteId))
		}
	}
	if flag {
		// 执行 BulkRequest
		bulkResponse, err := bulkRequest.Do(context.Background())
		if err != nil {
			glog.Error(fmt.Sprintf("UpdateGoodsToEs 批量删除出现异常 %s", err.Error()))
			return
		}
		// 检查是否有错误
		if bulkResponse.Errors {
			glog.Error(fmt.Sprintf("UpdateGoodsToEs 批量删除出现错误 %v", bulkResponse.Errors))
			return
		}
	}
	out.Code = 200
	return
}

func (p Product) ProductMemberPrice(ctx context.Context, in *sh.ProductMemberPriceRequest) (*sh.ProductMemberPriceResponse, error) {
	out := &sh.ProductMemberPriceResponse{}
	session := p.NewGetConn()
	defer session.Close()

	db := session.Table("upet_goods").Select("goods_id sku_id,enable_member_price").Where("goods_id IN (?) AND store_id=?", strings.Join(in.SkuIds, ","), in.OrgId)

	err := db.Find(&out.Data)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func BulkBySkuId(FinanceCode string, activityGoods map[string]*sh.GoodsActivity, good proto_models.UpetGoodsExt) proto_models.ChannelProductRequestEs {
	m := es.FormatUpetGoodsToChannelProductRequestEs(good)
	m.FinanceCode = FinanceCode
	//预售的ES需要特殊处理
	skuId := cast.ToString(good.GoodsId)
	m.Product.IsVip = 0
	if good.VipState == 1 {
		m.Product.IsVip = 1
	}
	if activityGoods[skuId] != nil {
		activityGood := activityGoods[skuId]
		if activityGoods[skuId].Type == 1 {
			if activityGood.IsEffective > 0 {
				m.SkuInfo[0].PromotionType = 11
				m.SkuInfo[0].PromotionPrice = activityGoods[skuId].Price
			} else {
				m.SkuInfo[0].PromotionType = 1
				m.SkuInfo[0].PromotionPrice = 0
			}
		}
	}
	return m
}
