package services

import (
	"_/models"
	"_/proto/mm"
	"_/utils"
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/shopspring/decimal"
)

type Member struct {
	BaseService
}

func (p Member) MemberMergeSearchList(ctx context.Context, in *mm.MemberMergeRequest) (*mm.MemberMergeResponse, error) {
	out := mm.MemberMergeResponse{Code: 400}
	session := p.NewGetConn()
	defer session.Close()
	var list []models.MemberMerge
	sql := fmt.Sprintf("select mml_notes,mml_new_mobile,mml_old_mobile,mml_dealtime,create_user,applicant,"+
		"department,application_date,approval_record from upet_member_merge_log where mml_new_mobile='%s' or mml_old_mobile='%s'",
		in.MemberPhone, in.MemberPhone)
	err := session.SQL(sql).Find(&list)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
	} else {
		out.Code = 200
		for _, v := range list {
			var applicationDate string
			if !v.ApplicationDate.IsZero() {
				applicationDate = v.ApplicationDate.Format(kit.DATE_LAYOUT)
			}
			data := mm.MemberMergelList{
				MmlDealtime:     v.MmlDealtime,
				MmlOldMobile:    v.MmlOldMobile,
				MmlNewMobile:    v.MmlNewMobile,
				MmlNotes:        v.MmlNotes,
				CreateUser:      v.CreateUser,
				Applicant:       v.Applicant,
				Department:      v.Department,
				ApplicationDate: applicationDate,
				ApprovalRecord:  v.ApprovalRecord,
			}
			out.Data = append(out.Data, &data)
		}
	}

	return &out, nil
}

//获取用户足迹列表
func (p Member) GetMemberGoodBrowseList(ctx context.Context, in *mm.GetMemberGoodBrowseListRequest) (*mm.GetMemberGoodBrowseListResponse, error) {
	out := &mm.GetMemberGoodBrowseListResponse{Code: 400}
	db := models.GetDBConn()

	var list []*models.UpetMemberBrowseGoodInfo
	sql := "SELECT a.*,goods_commonid spu_id,goods_price price,a.store_id,goods_name product_name,goods_image pic,goods_state up_down FROM upet_member_browse_log a JOIN upet_goods b on a.sku_id = b. goods_id " +
		"WHERE a.member_id=? AND a.store_id=? AND b.store_id=? ORDER BY a.create_time DESC LIMIT 100"
	err := db.SQL(sql, in.MemberId, in.OrgId, in.OrgId).Find(&list)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
	} else {
		out.Code = 200
		if len(list) == 0 {
			return out, nil
		}

		var ids []string
		for _, info := range list {
			ids = append(ids, fmt.Sprintf("%d", info.SkuId))
		}

		var wg sync.WaitGroup

		type Member struct {
			MemberId       int32
			NewcomerTag    int32
			MemberDiscount float64
			VipCardState   int32
		}
		// 先捞出会员等级及等级对应的折扣
		member := new(Member)
		if in.MemberId != "" {
			wg.Add(1)
			go func() {
				defer wg.Done()
				if _, err = db.Table("upet_member").Alias("m").Join("left", "datacenter.user_level l", "l.level_id = m.user_level_id").
					Where("m.scrm_user_id = ?", in.MemberId).Select("m.member_id,m.newcomer_tag,l.member_price as member_discount,m.vip_card_state").Get(member); err != nil {
					glog.Error("GetMemberGoodBrowseList " + err.Error())
				}
			}()
		} else {
			member.NewcomerTag = 1
		}

		type Goods struct {
			GoodsId           int32
			GoodsPrice        float64
			EnableMemberPrice int32
			VipDiscount       float64
			PromotionType     int32
			PromotionPrice    float64
		}
		pMap := make(map[int32]*Goods)
		// 商城商品统一查折扣
		if len(ids) > 0 {
			var pts []*Goods
			wg.Add(1)
			go func() {
				defer wg.Done()
				// 秒杀特殊入口
				if err = db.Table("upet_goods").Alias("g").
					Join("left", "upet_p_time p", "p.goods_id = g.goods_id and state = 1 and unix_timestamp() between start_time and end_time and promotion_type not in (9,6)").
					In("g.goods_id", ids).Where("g.store_id=? AND p.store_id=?", in.OrgId, in.OrgId).
					Select("g.goods_id,g.goods_price,g.enable_member_price,g.vip_discount,p.promotion_type,p.promotion_price").Find(&pts); err != nil {
					glog.Error("GetMemberGoodBrowseList " + err.Error())
				}

				for _, pt := range pts {
					pMap[pt.GoodsId] = pt
				}
			}()
		}

		wg.Wait()

		var data []*mm.MemberGoodBrowseInfo
		for _, v := range list {
			item := new(mm.MemberGoodBrowseInfo)
			item.CreateTime = v.CreateTime.Format(kit.DATETIME_LAYOUT)
			item.SkuId = v.SkuId
			item.SpuId = v.SpuId
			item.ProductName = v.ProductName
			//商品价格 单位分
			item.Price = int32(kit.YuanToFen(v.Price))
			item.Spec = v.Spec
			item.Pic = utils.HandleGoodsImage(v.Pic, 0, 0, v.StoreId)
			//上下架状态 0下架状态 1上架状态
			item.UpDown = v.UpDown
			item.Date = item.CreateTime[0:10]

			if pt, ok := pMap[item.SkuId]; ok {
				item.GoodsPromotionType = 0
				item.GoodsPromotionPrice = 0
				if pt.PromotionType > 0 && (pt.PromotionType != 7 || member.NewcomerTag == 1) {
					item.GoodsPromotionType = pt.PromotionType
					item.GoodsPromotionPrice = int32(decimal.NewFromFloat(pt.PromotionPrice * 100).Round(0).IntPart())
					if pt.PromotionType == 7 {
						item.NewPeoplePrice = item.GoodsPromotionPrice
					}
				} else if pt.PromotionType == 7 {
					pt.PromotionType = 0
				}

				if pt.PromotionType == 0 || pt.PromotionType == 2 {
					price := item.Price
					if item.GoodsPromotionPrice > 0 { // 折后折
						price = item.GoodsPromotionPrice
					}

					// 免费会员价
					if member.MemberDiscount > 0 && pt.EnableMemberPrice > 0 {
						freeVip := int32(decimal.NewFromFloat(member.MemberDiscount * float64(item.Price) / 10).Ceil().IntPart())
						if freeVip <= price {
							item.MemberPrice1 = freeVip
							item.IsMemberPrice = 1
						}
					}

					// 付费会员价
					if pt.VipDiscount > 0 {
						vip := int32(decimal.NewFromFloat(pt.VipDiscount * float64(price) / 10).Ceil().IntPart())
						// 付费会员价更低
						if item.MemberPrice1 == 0 || vip < item.MemberPrice1 {
							if member.VipCardState > 0 {
								item.IsMemberPrice = 1
								item.MemberPrice1 = vip
								if item.GoodsPromotionPrice > 0 {
									item.Price = int32(decimal.NewFromFloat(pt.VipDiscount * float64(item.Price) / 10).Ceil().IntPart())
								}
							} else if item.MemberPrice1 > 0 { // 没开通付费会员，且免费会员价格低
								item.Price = item.MemberPrice1
								item.IsMemberPrice = 0
							} else {
								item.Price = price
							}
							item.MemberPrice1 = vip
						}
					}

					if item.MemberPrice1 > 0 {
						item.GoodsPromotionType = 0
						item.GoodsPromotionPrice = 0
					}

					// 限时折扣会员价移除水印
					if pt.PromotionType == 2 && item.IsMemberPrice > 0 {
						item.Pic = strings.Split(item.Pic, "?")[0]
					}
				}

				if item.MemberPrice1 == 0 && item.GoodsPromotionPrice > 0 {
					item.Price = item.GoodsPromotionPrice
				}

			}

			data = append(data, item)
		}
		out.Data = data
	}
	return out, nil
}

//保存足迹
func (p Member) StoreMemberGoodBrowseInfo(ctx context.Context, in *mm.StoreMemberGoodBrowseInfoRequest) (*mm.StoreMemberGoodBrowseInfoResponse, error) {
	out := &mm.StoreMemberGoodBrowseInfoResponse{Code: 400}
	session := p.NewGetConn()
	defer session.Close()
	data := new(models.UpetMemberBrowseLog)
	data.CreateTime = time.Now()
	data.MemberId = in.MemberId
	data.SkuId = in.SkuId
	//获取spu
	var spuId int32
	_, err := session.SQL("SELECT goods_commonid spu_id FROM upet_goods WHERE goods_id = ? AND store_id=?", in.SkuId, in.OrgId).Get(&spuId)
	data.SpuId = spuId
	data.StoreId = in.OrgId

	if spuId != 0 {
		//清除之前的记录
		_, err = session.Exec("DELETE FROM upet_member_browse_log WHERE member_id=? AND spu_id=? AND store_id=?", in.MemberId, spuId, in.OrgId)
	}
	//上下架状态 0下架状态 1上架状态
	_, err = session.Insert(data)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
	} else {
		out.Code = 200
	}
	return out, nil
}

//清除足迹
func (p Member) ClearMemberGoodBrowse(ctx context.Context, in *mm.ClearMemberGoodBrowseRequest) (*mm.ClearMemberGoodBrowseResponse, error) {
	out := &mm.ClearMemberGoodBrowseResponse{Code: 400}
	session := p.NewGetConn()
	defer session.Close()
	var model models.UpetMemberBrowseLog
	_, err := session.Where("member_id=?", in.MemberId).Delete(&model)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
	} else {
		out.Code = 200
	}
	return out, nil
}
