package services

import (
	"_/models"
	"_/proto/ic"
	"context"
	"database/sql"
	"encoding/json"
	"runtime"
	"strconv"
	"time"

	"github.com/go-redis/redis"
	_ "github.com/go-sql-driver/mysql"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

type BaseService struct {
}

func (b *BaseService) NewGetConn() *xorm.Engine {
	params := config.GetString("mysql.upetmart")
	engine, err := xorm.NewEngine("mysql", params)
	//engine.ShowSQL()
	//err = engine.Ping()
	if err != nil {
		panic(err)
	}
	return engine
}

func GetProductGrpcConn() *grpc.ClientConn {
	GrpcAddress := config.GetString("grpc.product-center")
	if GrpcAddress == "" || runtime.GOOS == "windows" {
		GrpcAddress = "127.0.0.1:11003"
	}
	conn, err := grpc.Dial(GrpcAddress, grpc.WithInsecure())
	if err != nil {
		//glog.Fatalf("did not connect: %v", err)
	}
	return conn
}

func (b *BaseService) GetConnNative() *sql.DB {
	params := config.GetString("mysql.dc_order")
	var err error
	engine, err := sql.Open("mysql", params)
	if err != nil {
		panic(err)
	}
	err = engine.Ping()
	return engine
}

//根据语句查询总记录数
func (b *BaseService) CountPage(sql string, params []interface{}) int {
	services := BaseService{}
	conn := services.GetConnNative()
	totle := 0
	stmt, err := conn.Prepare(sql)
	if err != nil {
		return 0
	}
	result, err := stmt.Query(params...)
	if err != nil {
		return 0
	}
	result.Next()
	result.Scan(&totle)
	return totle
}

//获取Dispatch-center服务客户端
func (b *BaseService) GetDispatchClient() *grpc.ClientConn {
	address := config.GetString("grpc.dispatch-center")
	if address == "" || runtime.GOOS == "windows" {
		address = "127.0.0.1:11006"
	}
	conn, err := grpc.Dial(address, grpc.WithInsecure())
	if err != nil {
		glog.Errorf("did not connect: %v", err)
	}
	return conn
}

//获取datacenter服务客户端
func (b *BaseService) GetDataCenterClient() *grpc.ClientConn {
	address := config.GetString("grpc.data-center")
	if address == "" || runtime.GOOS == "windows" {
		address = "127.0.0.1:10032"
	}
	//address = "**********:10032"
	conn, err := grpc.Dial(address, grpc.WithInsecure())
	if err != nil {
		glog.Errorf("did not connect: %v", err)
	}
	return conn
}

// 获取商品中心数据库
func (b *BaseService) GetProductClient() *grpc.ClientConn {
	address := config.GetString("grpc.product")
	if address == "" || runtime.GOOS == "windows" {
		address = "127.0.0.1:11003"
	}
	conn, err := grpc.Dial(address, grpc.WithInsecure())
	if err != nil {
		glog.Errorf("did not connect: %v", err)
	}
	return conn
}

//获取datacenter服务客户端
func (b *BaseService) GetExternalClient() *grpc.ClientConn {
	address := config.GetString("grpc.external")
	if address == "" || runtime.GOOS == "windows" {
		address = "127.0.0.1:11031"
	}
	conn, err := grpc.Dial(address, grpc.WithInsecure())
	if err != nil {
		glog.Errorf("did not connect: %v", err)
	}
	return conn
}

// 建立InventoryCenter 的grpc 连接公共方法
func (b *BaseService) GetInventoryClient() (*grpc.ClientConn, context.Context, ic.InventoryServiceClient, context.CancelFunc) {
	inventoryAddress := config.GetString("grpc.inventory-center")
	if inventoryAddress == "" || runtime.GOOS == "windows" {
		inventoryAddress = "127.0.0.1:11007"
	}
	if conn, err := grpc.Dial(inventoryAddress, grpc.WithInsecure()); err != nil {
		glog.Error("连接GRPC发生错误：", err)
		return nil, nil, nil, nil
	} else {
		client := ic.NewInventoryServiceClient(conn)
		ctx, cf := context.WithTimeout(context.Background(), time.Second*30)
		return conn, ctx, client, cf
	}
}

//redis链接
func ConnectRedis() *redis.Client {
	DB, _ := strconv.Atoi(config.GetString("redis.DB"))
	client := redis.NewClient(&redis.Options{
		Addr:     config.GetString("redis.Addr"),
		Password: config.GetString("redis.Password"),
		DB:       DB,
	})
	_, err := client.Ping().Result()
	if err != nil {
		panic(err)
	}
	return client
}

func (b *BaseService) LoadLoginUserInfo(ctx context.Context) *models.LoginUserInfo {
	var userInfo models.LoginUserInfo
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if err := json.Unmarshal([]byte(md.Get("login_user_info")[0]), &userInfo); err != nil {
			glog.Error(err)
		}
	} else {
		glog.Error("grpc context 加载用户登录信息失败")
	}
	if userInfo.UserNo == "" {
		return nil
	} else {
		return &userInfo
	}
}

func (b *BaseService) LoadGrpcContext(ctx context.Context) *models.GrpcContext {
	var GrpcContext models.GrpcContext
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if err := json.Unmarshal([]byte(md.Get("grpc_context")[0]), &GrpcContext); err != nil {
			glog.Error(err)
		}
	} else {
		glog.Error("grpc context 加载用户登录信息失败")
	}

	return &GrpcContext
}
