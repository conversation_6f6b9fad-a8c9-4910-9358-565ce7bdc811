package services

import (
	"_/proto/sh"
	"context"
	"fmt"
	"testing"
)

func TestActivityService_LuckyNumSave(t *testing.T) {
	var (
		as  ActivityService
		ctx context.Context
	)
	out, err := as.LuckyNumSave(ctx, &sh.LuckyNumSaveRequest{
		LuckyNum:       "045F6E",
		LotteryId:      23,
		Type:           3,
		ScrmId:         "e10adc3949ba59abbe56e057f20f883e",
		InviteMemberId: 123456,
	})
	fmt.Println(out, err)
}
