package services

import (
	"_/models"
	"_/proto/sh"
	"context"
	"fmt"
	"strings"

	"github.com/spf13/cast"
)

// 获取用户等级
func (p Product) UserLevel(ctx context.Context, in *sh.UserLevelRequest) (*sh.UserLevelResponse, error) {
	resp := &sh.UserLevelResponse{Code: 400, UserEquities: make([]*sh.UserEquity, 0)}
	member, err := new(models.UpetMember).FindByScrmUserId(in.ScrmUserId)
	if err != nil {
		resp.Message = err.Error()
		return resp, nil
	}
	resp.UserLevel = fmt.Sprintf("VIP%d", member.UserLevelId)
	resp.UserLevelId = member.UserLevelId
	resp.UserLevelStime = member.UserLevelStime
	resp.UserLevelEtime = member.UserLevelEtime
	// 获取会员权益
	if in.WithEquity == 1 {
		resp.UserEquities = getUserLevelEquities(member.UserLevelId)
	}

	resp.Code = 200
	return resp, nil
}

// 获取会员权益
func getUserLevelEquities(levelId int64) []*sh.UserEquity {
	list := make([]*sh.UserEquity, 0)
	userLevel, err := new(models.UserLevel).FindByLevelId(levelId)
	if err != nil {
		return list
	}
	arr := strings.Split(userLevel.PrivilegeIds, ",")
	equityIds := make([]int64, 0, len(arr))
	for _, v := range arr {
		equityIds = append(equityIds, cast.ToInt64(v))
	}
	equity, _ := new(models.UserEquity).FindByIds(equityIds)
	for _, v := range equity {
		icon := ""
		for _, c := range v.Icons {
			if c.LevelId == levelId {
				icon = c.Icon
			}
		}
		list = append(list, &sh.UserEquity{
			Id:         v.Id,
			EquityName: v.EquityName,
			Icon:       icon,
			EquityInfo: v.EquityInfo,
		})
	}
	return list
}
