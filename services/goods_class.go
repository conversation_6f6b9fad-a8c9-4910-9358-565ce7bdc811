package services

import (
	"_/models"
	"_/proto/sh"
	"_/utils"
	"context"
	"errors"

	"github.com/spf13/cast"
)

// 获取商品分类信息
func (p Product) GetGoodsClasses(ctx context.Context, in *sh.GetGoodsClassesRequest) (*sh.GetGoodsClassesResponse, error) {
	re := &sh.GetGoodsClassesResponse{
		Data: &sh.GetGoodsClassesResponse_Data{
			ChildrenList:     make([]*sh.GetGoodsClassesResponse_GoodsClass, 0),
			ParentGoodsClass: make([]*sh.GetGoodsClassesResponse_GoodsClass, 0),
		},
	}

	// 父类
	parentClass, err := new(models.UpetGoodsClass).FindByIds(in.GcIds)
	if err != nil {
		re.Code = 400
		re.Message = "查询商品分类信息失败1"
		return re, nil
	}
	for _, v := range parentClass {
		re.Data.ParentGoodsClass = append(re.Data.ParentGoodsClass, &sh.GetGoodsClassesResponse_GoodsClass{
			GcId:       v.GcId,
			GcName:     v.GcName,
			GcParentId: v.GcParentId,
		})
	}

	// 子类
	childrenClass, err := new(models.UpetGoodsClass).FindByParentIds(in.GcIds)
	if err != nil {
		re.Code = 400
		re.Message = "查询商品分类信息失败2"
		return re, nil
	}
	for _, v := range childrenClass {
		re.Data.ChildrenList = append(re.Data.ChildrenList, &sh.GetGoodsClassesResponse_GoodsClass{
			GcId:       v.GcId,
			GcName:     v.GcName,
			GcParentId: v.GcParentId,
		})
	}
	re.Code = 200
	return re, nil
}

// 获取会员商品分类
func (p Product) GoodsClassList(ctx context.Context, in *sh.GoodsClassListReq) (*sh.GoodsClassListRes, error) {
	resp := &sh.GoodsClassListRes{}

	db := models.GetDBConn()

	var err error
	if in.GcId == 0 {
		err = db.Table("`upet_goods` `ug`").Join("INNER", "`upet_goods_class` `ugc`", "ug.gc_id_1 =ugc.gc_id").
			Select("ug.gc_id_1 gc_id,ugc.gc_name gc_name").Where("ug.gc_id_1 > 0 AND ug.gc_id_2 > 0 AND ug.enable_member_price=1 AND ug.store_id=?", in.OrgId).
			GroupBy("ug.gc_id_1").OrderBy("ug.gc_id_1 asc").Find(&resp.Data)
	} else {
		err = db.Table("`upet_goods` `ug`").Join("INNER", "`upet_goods_class` `ugc`", "ug.gc_id_2 =ugc.gc_id").
			Select("ug.gc_id_2 gc_id,ugc.gc_name gc_name").Where("ug.gc_id_1 = ? AND ug.enable_member_price=1 AND ug.store_id=", in.GcId, in.OrgId).
			GroupBy("ug.gc_id_2").OrderBy("ug.gc_id_2 asc").Find(&resp.Data)
	}
	if err != nil {
		resp.Msg = err.Error()
		return resp, err
	}

	return resp, nil
}

// 获取会员商品列表
// 1、未登录情况：商品已开启会员价，商品VIP价格=【已启用 &&  且勾选 "会员价"  的最高等级】的价格
// 2、已登录情况：商品已开启会员价，当前会员等级已启用 && 且勾选 "会员价"，此时才有商品返回；其他情况没有商品返回
func (p Product) GoodsList(ctx context.Context, in *sh.GoodsListReq) (*sh.GoodsListRes, error) {
	out := new(sh.GoodsListRes)
	db := models.GetDBConn()

	userLevels, err := new(models.UserLevel).FindUserLevelsMap()
	if err != nil {
		return out, err
	}

	//处理会员价逻辑，前端只展示会员价大于0的商品
	levelMemberPrice := 0.0
	if in.ScrmUserid != "" {
		//查询用户当前级别
		member := models.UpetMember{}
		has, err := db.Table("upet_member").Cols("user_level_id,user_level_etime").Where("scrm_user_id=?", in.ScrmUserid).Get(&member)
		if err != nil {
			return out, err
		}
		if !has {
			return out, errors.New("用户信息未找到")
		}
		levelMemberPrice = cast.ToFloat64(userLevels[member.UserLevelId].MemberPrice)
		if userLevels[member.UserLevelId].LevelStatus != 1 || levelMemberPrice == 0 {
			out.Msg = "用户等级会员价未开启"
			return out, nil
		}
	} else {
		hasPrice := false
		for i := int64(8); i >= 0; i-- {
			if v, ok := userLevels[i]; ok && v.LevelStatus == 1 && cast.ToFloat64(v.MemberPrice) > 0 {
				hasPrice = true
				levelMemberPrice = cast.ToFloat64(v.MemberPrice)
				break
			}
		}
		if !hasPrice {
			return out, nil
		}
	}

	session := db.Table("upet_goods").Alias("g").
		Join("left", "upet_goods_common as c", "g.goods_commonid=c.goods_commonid").
		Where("g.goods_state=1 and g.goods_verify=1 and g.enable_member_price=1 AND g.goods_promotion_type=0 AND g.store_id=? "+
			"and not exists( select 1 from upet_p_time pt where pt.state = 1 and unix_timestamp() between pt.start_time and pt.end_time and pt.goods_id = g.goods_id AND pt.store_id=?)", in.OrgId, in.OrgId)
	if in.GoodsName != "" {
		session.And("g.goods_name like ?", "%"+in.GoodsName+"%")
	}
	if in.GcId > 0 {
		session.And("g.gc_id=? or g.gc_id_1=? or g.gc_id_2=? or g.gc_id_3=?", in.GcId, in.GcId, in.GcId, in.GcId)
	}

	// 过滤付费会员价格
	session.And("g.vip_discount = 0")

	if in.Type > 0 && in.Sort > 0 {
		sort := "asc"
		if in.Sort == 2 {
			sort = "desc"
		}

		if in.Type == 1 {
			session.OrderBy("g.goods_salenum " + sort)
		} else {
			session.OrderBy("g.goods_price " + sort)
		}
	} else {
		session.OrderBy("c.goods_addtime desc")
	}

	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}

	countSession := *session

	count, _ := countSession.Count()
	out.Total = int32(count)
	if err := session.Select("goods_id, c.goods_jingle,g.goods_commonid, g.color_id, g.store_id, g.goods_salenum, g.enable_member_price, c.short_name, g.goods_type, g.is_virtual, goods_promotion_price, goods_promotion_type, g.goods_name, g.goods_price, g.goods_image").
		Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).Find(&out.Data); err != nil {
		return out, err
	}
	for k, v := range out.Data {
		out.Data[k].IsMemberPrice = 1
		out.Data[k].MemberPrice = float32(utils.CalculateMemberPrice(float64(v.GoodsPrice), levelMemberPrice))
		out.Data[k].GoodsImage = utils.HandleGoodsImage(v.GoodsImage, v.GoodsCommonid, v.ColorId, v.StoreId)
	}

	return out, nil
}
