package services

import (
	"_/models"
	"_/proto/sh"
	"context"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"testing"
)

func TestDistributionActivity_DisLimitActivityCommissionSet(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisLimitActivityCommissionSetRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			da := &DistributionActivity{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := da.DisLimitActivityCommissionSet(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisLimitActivityCommissionSet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.<PERSON><PERSON>rf("DisLimitActivityCommissionSet() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestDistributionActivity_DisLimitActivityGoodsDelete(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisLimitActivityGoodsDeleteRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			da := &DistributionActivity{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := da.DisLimitActivityGoodsDelete(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisLimitActivityGoodsDelete() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DisLimitActivityGoodsDelete() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestDistributionActivity_DisLimitActivityGoodsImport(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisLimitActivityGoodsImportRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			da := &DistributionActivity{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := da.DisLimitActivityGoodsImport(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisLimitActivityGoodsImport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DisLimitActivityGoodsImport() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestDistributionActivity_DisLimitActivityList(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisLimitActivityListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisLimitActivityListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			da := &DistributionActivity{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := da.DisLimitActivityList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisLimitActivityList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DisLimitActivityList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestDistributionActivity_DisLimitActivityLog(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisLimitActivityLogRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisSpuLogsResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			da := &DistributionActivity{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := da.DisLimitActivityLog(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisLimitActivityLog() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DisLimitActivityLog() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestDistributionActivity_DisLimitActivityOperate(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisLimitActivityOperateRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			da := &DistributionActivity{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := da.DisLimitActivityOperate(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisLimitActivityOperate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DisLimitActivityOperate() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestDistributionActivity_DisLimitActivityStop(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		ctx context.Context
		in  *sh.DisLimitActivityStopRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *sh.DisResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			da := &DistributionActivity{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotOut, err := da.DisLimitActivityStop(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisLimitActivityStop() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DisLimitActivityStop() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestDistributionActivity_addLimitActivity(t *testing.T) {
	type fields struct {
		DistributionCommon DistributionCommon
	}
	type args struct {
		disCommissionActivity models.UpetDisCommissionActivity
		in                    *sh.DisLimitActivityOperateRequest
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		wantActivityId int64
		wantErr        bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			da := &DistributionActivity{
				DistributionCommon: tt.fields.DistributionCommon,
			}
			gotActivityId, err := da.addLimitActivity(tt.args.disCommissionActivity, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("addLimitActivity() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotActivityId != tt.wantActivityId {
				t.Errorf("addLimitActivity() gotActivityId = %v, want %v", gotActivityId, tt.wantActivityId)
			}
		})
	}
}

func TestDistributionActivity_editLimitActivity(t *testing.T) {

	//str := "a:1:{i:36;s:22:'牛肉蔬菜罐头375g';}"
	str := `a:2:{i:236;s:9:"鸡肉味";i:240;s:16:"8寸（265g）*6";}`
	reg := regexp.MustCompile(`"(.*?)"`)
	s := reg.FindAllString(str, -1)
	var rStr string
	if len(s) > 0 {
		for _, v := range s {
			rStr = rStr + strings.Trim(v, `""`)
		}
	}
	fmt.Println(rStr)
	//type fields struct {
	//	DistributionCommon DistributionCommon
	//}
	//type args struct {
	//	disCommissionActivity models.UpetDisCommissionActivity
	//	in                    *sh.DisLimitActivityOperateRequest
	//}
	//tests := []struct {
	//	name           string
	//	fields         fields
	//	args           args
	//	wantLogContent string
	//	wantErr        bool
	//}{
	//	// TODO: Add test cases.
	//}
	//for _, tt := range tests {
	//	t.Run(tt.name, func(t *testing.T) {
	//		da := &DistributionActivity{
	//			DistributionCommon: tt.fields.DistributionCommon,
	//		}
	//		gotLogContent, err := da.editLimitActivity(tt.args.disCommissionActivity, tt.args.in)
	//		if (err != nil) != tt.wantErr {
	//			t.Errorf("editLimitActivity() error = %v, wantErr %v", err, tt.wantErr)
	//			return
	//		}
	//		if gotLogContent != tt.wantLogContent {
	//			t.Errorf("editLimitActivity() gotLogContent = %v, want %v", gotLogContent, tt.wantLogContent)
	//		}
	//	})
	//}
}

func TestDistributionActivity_DisLimitActivityGoodsList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *sh.DisLimitActivityGoodsListRequest
	}
	tests := []struct {
		name    string
		da      *DistributionActivity
		args    args
		wantOut *sh.DisLimitActivityGoodsListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "测试",
			args: args{
				ctx: context.Background(),
				in: &sh.DisLimitActivityGoodsListRequest{
					SearchType: 1,
					ActivityId: 15,
					PageSize:   10,
					Page:       1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.da.DisLimitActivityGoodsList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DistributionActivity.DisLimitActivityGoodsList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DistributionActivity.DisLimitActivityGoodsList() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}
