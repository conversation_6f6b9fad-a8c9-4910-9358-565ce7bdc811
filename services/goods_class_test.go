package services

import (
	"_/proto/sh"
	"context"
	"reflect"
	"testing"
)

func TestProduct_GetGoodsClasses(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *sh.GetGoodsClassesRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *sh.GetGoodsClassesResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:   "test GetGoodsClasses",
			fields: fields{BaseService: BaseService{}},
			args: args{in: &sh.GetGoodsClassesRequest{
				GcIds: []int64{1057, 1058},
			}},
			want:    &sh.GetGoodsClassesResponse{Code: 200},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := Product{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.GetGoodsClasses(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.GetGoodsClasses() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Code != tt.want.Code {
				t.Errorf("Product.GetGoodsClasses() = %v, want %v", got, tt.want)
			} else {
				t.Log(got.Data)
			}
		})
	}
}

func TestProduct_GoodsList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *sh.GoodsListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *sh.GoodsListRes
		wantErr bool
	}{
		{
			args: args{in: &sh.GoodsListReq{
				GoodsName: "",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := Product{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.GoodsList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GoodsList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GoodsList() got = %v, want %v", got, tt.want)
			}
		})
	}
}
