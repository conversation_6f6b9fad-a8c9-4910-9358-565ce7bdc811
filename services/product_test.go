package services

import (
	"_/proto/sh"
	"context"
	kit "github.com/tricobbler/rp-kit"
	"reflect"
	"testing"
)

func TestProduct_UpdateGoodsToEs(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *sh.GoodsToEsUpdateRequest
	}
	tests := []struct {
		name    string
		p       Product
		args    args
		want    *sh.BaseResponse
		wantErr bool
	}{
		{
			name: "",
			p:    Product{},
			args: args{
				ctx: context.Background(),
				in:  &sh.GoodsToEsUpdateRequest{Ids: "1051430001", DsOrgId: 3, ShopId: 0},
				//in: &sh.GoodsToEsUpdateRequest{Ids: "1533416507", DsOrgId: 1, ShopId: 0},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.p.UpdateGoodsToEs(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Product.UpdateGoodsToEs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Product.UpdateGoodsToEs() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_ProductMemberPrice(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *sh.ProductMemberPriceRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *sh.ProductMemberPriceResponse
		wantErr bool
	}{
		{name: "测试获取会员价格",
			args: args{
				ctx: context.Background(),
				in: &sh.ProductMemberPriceRequest{
					SkuIds:      []string{"1000113", "1000114"},
					MemberLevel: 1,
				},
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := Product{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.ProductMemberPrice(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProductMemberPrice() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProductMemberPrice() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_GoodsRelevanceList(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *sh.SkuRelListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *sh.SkuRelListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &sh.SkuRelListRequest{
					PageIndex: 1,
					PageSize:  10,
					StoreId:   1,
					Keyword:   "",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := Product{
				BaseService: tt.fields.BaseService,
			}
			got, err := p.GoodsRelevanceList(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(got))
			if (err != nil) != tt.wantErr {
				t.Errorf("GoodsRelevanceList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
