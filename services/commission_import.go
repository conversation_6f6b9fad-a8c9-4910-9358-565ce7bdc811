package services

import (
	"_/helpers"
	"_/models"
	"_/proto/sh"
	"context"
	"errors"
	"fmt"

	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

type CommissionImportInfo struct {
	ResultUrl     string
	ResultContent string
}

var (
	SuccessNum = 0
	FailedNum  = 0
	MaxNum     = 10
	CurRowANum = 0
	fWrite     *excelize.StreamWriter
	ImportInfo CommissionImportInfo
)

// 限时佣金excel导入
func CommissionImport(ctx context.Context, f *excelize.File, activityId int64, orgId int32) (err error, out CommissionImportInfo) {
	defer func() {
		if error := recover(); error != nil {
			glog.Error("限时佣金商品导入报错，", error)
			err = errors.New(cast.ToString(error))
		}
	}()

	// 获取 Sheet1 上所有单元格
	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return
	}
	if len(rows) <= 1 {
		err = errors.New("表格无数据，请检查")
		return
	}

	// 导入结果excel记录
	file := excelize.NewFile()
	defer file.Close()
	fWrite, _ = file.NewStreamWriter("Sheet1")
	fWrite.SetRow("A1", []interface{}{
		"商品SKU", "活动佣金", "导入结果", "原因", "导入时的日常佣金",
	})

	// 根据maxNum 分组
	SuccessNum = 0
	FailedNum = 0
	if len(rows) <= MaxNum {
		CurRowANum = 2
		insertRows(ctx, rows[1:], activityId, orgId)
	} else {
		var data [][]string
		totalValue, _ := decimal.NewFromInt32(cast.ToInt32(len(rows))).Div(decimal.NewFromInt32(cast.ToInt32(MaxNum))).Ceil().Value()
		total := cast.ToInt(totalValue)
		for i := 0; i < total; i++ {
			s := i * MaxNum
			e := s + MaxNum
			CurRowANum = s + 1 // 记录excelRowA行数
			if i == 0 {
				CurRowANum = 2
				data = rows[1:e]
			} else if i == total-1 {
				data = rows[s:]
			} else {
				data = rows[s:e]
			}
			insertRows(ctx, data, activityId, orgId)
		}
	}
	if err = fWrite.Flush(); err != nil {
		return
	}
	// 结果汇总
	if ImportInfo.ResultUrl, err = helpers.UploadExcelToQiNiu(file, ""); err != nil {
		return
	}
	if FailedNum < 1 {
		ImportInfo.ResultContent = "全部成功：" + cast.ToString(SuccessNum)
	} else if SuccessNum < 1 {
		ImportInfo.ResultContent = "全部失败：" + cast.ToString(FailedNum)
	} else {
		ImportInfo.ResultContent = "成功：" + cast.ToString(SuccessNum) + "，失败：" + cast.ToString(FailedNum)
	}

	out = ImportInfo
	return
}

func insertRows(ctx context.Context, rows [][]string, activityId int64, orgId int32) {
	logPrefix := fmt.Sprintf("限时佣金/活动商品佣金批量设置======入参:orgId=%d,activityId=%d,rows=%s", orgId, activityId, kit.JsonEncode(rows))
	glog.Info(logPrefix)
	// A、获取sku_id的商品数据
	skuIds := make([]string, len(rows))
	skuCommission := make(map[string]string, len(rows))
	for _, r := range rows {
		if len(r) < 2 {
			continue
		}
		if r[0] != "" {
			skuIds = append(skuIds, r[0])
			skuCommission[r[0]] = r[1]
		}
	}
	db := models.GetDBConn()
	var goods []*models.UpetGoods
	db.Select("goods_id,goods_state,is_dis,IF(dis_normal_commis_rate > 0,dis_normal_commis_rate,dis_commis_rate) dis_commis_rate,dis_write").
	In("goods_id", skuIds).Where("store_id=?", orgId).Find(&goods)
	goodsInfo := make(map[int]*models.UpetGoods, len(goods))
	if len(goods) > 0 {
		for k, _ := range goods {
			goodsInfo[goods[k].GoodsId] = goods[k]
		}
	}

	// B、遍历导入的表格数据，记录成功失败状态
	var da DistributionActivity
	for i, r := range rows {
		if len(r) < 2 {
			continue
		}
		if r[0] == "" {
			continue
		}
		rate := cast.ToFloat32(r[1])
		rowA := "A" + cast.ToString(CurRowANum+i)
		if g, ok := goodsInfo[cast.ToInt(r[0])]; !ok {
			fWrite.SetRow(rowA, []interface{}{
				cast.ToInt(r[0]), cast.ToFloat32(r[1]), "失败", "sku不存在", "-",
			})
			FailedNum = FailedNum + 1
			continue
		} else {
			failedWord := ""
			if g.IsDis != 1 {
				failedWord = "非分销商品"
			} else if g.GoodsState != 1 {
				failedWord = "商品未上架"
			} else if rate < 0.5 || rate > 50 {
				failedWord = "活动佣金必须在0.5%-50%"
			} else if g.DisNormalCommisRate > 0 {
				if g.DisNormalCommisRate == rate {
					failedWord = "活动佣金和日常佣金不能相等"
				}
			} else if g.DisCommisRate == rate {
				failedWord = "活动佣金和日常佣金不能相等"
			}
			// 失败
			if failedWord != "" {
				fWrite.SetRow(rowA, []interface{}{
					cast.ToInt(r[0]), cast.ToFloat32(r[1]), "失败", failedWord, g.DisCommisRate,
				})
				FailedNum = FailedNum + 1
				continue
			}

			// 插入活动商品表
			request := &sh.DisLimitActivityCommissionSetRequest{
				ActivityId: activityId,
				OrgId:      orgId,
			}
			request.ActivityCommission = append(request.ActivityCommission, &sh.DisLimitActivityCommissionData{
				SkuId:              cast.ToInt64(r[0]),
				ActivityCommission: rate,
			})
			out, err := da.DisLimitActivityCommissionSet(ctx, request)
			if err != nil || out.Code != 200 {
				fWrite.SetRow(rowA, []interface{}{
					cast.ToInt(r[0]), cast.ToFloat32(r[1]), "失败", out.Message, g.DisCommisRate,
				})
				FailedNum = FailedNum + 1
				continue
			}

			// 成功
			fWrite.SetRow(rowA, []interface{}{
				cast.ToInt(r[0]), cast.ToFloat32(r[1]), "成功", "", g.DisCommisRate,
			})
			SuccessNum = SuccessNum + 1
		}
	}
}
