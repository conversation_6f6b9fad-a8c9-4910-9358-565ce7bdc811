package services

import (
	"_/models"
	"_/proto/sh"
	"_/utils"
	"context"
	"fmt"
	"github.com/spf13/cast"
	"math/rand"
	"strings"
	"time"
)

type Voucher struct {
}

// 回收优惠券
func (m Voucher) RecoverUserVoucher(ctx context.Context, in *sh.RecoverUserVoucherRequest) (*sh.Response, error) {
	resp := &sh.Response{Code: 400}
	scrmUserIds := make([]string, 0)
	for _, v := range in.UserVoucher {
		scrmUserIds = append(scrmUserIds, v.ScrmUserId)
	}
	members, err := new(models.UpetMember).FindMemberIdByUserIds(scrmUserIds)
	if err != nil {
		resp.Message = err.Error()
		return resp, nil
	}

	for _, v := range in.UserVoucher {
		if mId, ok := members[v.ScrmUserId]; ok {
			err := new(models.UpetVoucher).RecoverUserVoucher(mId, v.VoucherTIds)
			if err != nil {
				resp.Message = err.Error()
				return resp, nil
			}
		}
	}

	resp.Code = 200
	return resp, nil
}

// 获取已使用的优惠券用户手机
func (m Voucher) FindUsedVoucherUserMobile(ctx context.Context, in *sh.FindUsedVoucherUserMobileRequest) (*sh.FindUsedVoucherUserMobileResponse, error) {
	resp := &sh.FindUsedVoucherUserMobileResponse{Code: 400, List: make([]*sh.UsedVoucherUserMobile, 0)}
	page, size := 1, 1000
	usedUserMobile := make(map[int64]*sh.UsedVoucherUserMobile, 0)
	for {
		list, err := new(models.UpetVoucher).FindUsedListByVoucherTIds(in.VoucherTIds, page, size)
		if err != nil {
			resp.Message = err.Error()
			return resp, nil
		}
		if len(list) == 0 {
			break
		}
		tMemberIds := make(map[int64][]int64, 0)
		ownerIds := make([]int64, 0)
		for _, v := range list {
			if !utils.InArray(v.VoucherOwnerId, ownerIds) {
				ownerIds = append(ownerIds, v.VoucherOwnerId)
			}
			if !utils.InArray(v.VoucherOwnerId, tMemberIds[v.VoucherTId]) {
				tMemberIds[v.VoucherTId] = append(tMemberIds[v.VoucherTId], v.VoucherOwnerId)
			}
		}
		members, err := new(models.UpetMember).FindMobileByMemberIds(ownerIds)
		if err != nil {
			resp.Message = err.Error()
			return resp, nil
		}
		for tId, mids := range tMemberIds {
			if _, ok := usedUserMobile[tId]; !ok {
				usedUserMobile[tId] = &sh.UsedVoucherUserMobile{
					VoucherTId: tId,
				}
			}
			for _, mid := range mids {
				if m, ok := members[mid]; ok && m != "" {
					usedUserMobile[tId].Mobiles = append(usedUserMobile[tId].Mobiles, m)
				}
			}
		}
		page++
	}
	for _, v := range usedUserMobile {
		resp.List = append(resp.List, v)
	}
	resp.Code = 200
	return resp, nil
}

// 发送商城优惠券
func (m Voucher) IssueAwardByMallCoupon(ctx context.Context, in *sh.IssueAwardByMallCouponRequest) (*sh.IssueAwardByMallCouponResponse, error) {
	resp := &sh.IssueAwardByMallCouponResponse{Code: 400}
	if len(in.CouponId) < 1 {
		resp.Message = "优惠券模板id不能为空"
		return resp, nil
	}

	db := models.GetDBConn()
	member := new(models.UpetMember)
	has, err := db.Where("scrm_user_id = ?", in.UserId).Get(member)
	if err != nil {
		resp.Message = "发券查询会员出错 " + err.Error()
		return resp, nil
	}
	if !has {
		resp.Message = "兑换商城券时，会员不存在"
		return resp, nil
	}

	var tpls []*models.UpetVoucherTemplate
	var vouchers []*models.UpetVoucher
	err = db.Table("upet_voucher_template").Alias("t").
		In("voucher_t_id", strings.Split(in.CouponId, ",")).
		Where("voucher_t_state = 1").
		Where("voucher_t_giveout < voucher_t_total").
		Where(fmt.Sprintf("voucher_t_eachlimit > ifnull((%s),0)",
			"select count(1) from upet_voucher v where v.voucher_t_id = t.voucher_t_id and v.voucher_owner_id = "+cast.ToString(member.MemberId))).
		Find(&tpls)

	if err != nil {
		resp.Message = "查询优惠券模板出错 " + err.Error()
		return resp, nil
	}
	if len(tpls) == 0 {
		resp.Message = "暂无可领取的优惠券"
		return resp, nil
	}

	var tplIds []string
	var data []*sh.IssueAwardCoupon
	for i, tpl := range tpls {
		voucher := getVoucherByTemplate(i, tpl, member)

		tplIds = append(tplIds, cast.ToString(tpl.VoucherTId))
		vouchers = append(vouchers, voucher)
		data = append(data, &sh.IssueAwardCoupon{
			CouponCode:    voucher.VoucherCode,
			CouponEndTime: voucher.VoucherEndDate,
		})
	}

	if _, err = db.Insert(vouchers); err != nil {
		resp.Message = "派发优惠券出错 " + err.Error()
		return resp, nil
	}
	// 统计数量更新
	_, _ = db.Exec("update upet_voucher_template set voucher_t_giveout = voucher_t_giveout + 1 " +
		"where voucher_t_id in (" + strings.Join(tplIds, ",") + ")")

	resp.Data = data
	resp.Code = 200
	resp.Message = "success"
	return resp, nil
}

// 通过模板获取代金券
func getVoucherByTemplate(i int, tpl *models.UpetVoucherTemplate, member *models.UpetMember) *models.UpetVoucher {
	voucher := &models.UpetVoucher{
		VoucherCode:       generateVoucherCode(int32(member.MemberId), i),
		VoucherTId:        tpl.VoucherTId,
		VoucherTitle:      tpl.VoucherTTitle,
		VoucherDesc:       tpl.VoucherTDesc,
		VoucherPrice:      tpl.VoucherTPrice,
		VoucherLimit:      tpl.VoucherTLimit,
		VoucherStoreId:    tpl.VoucherTStoreId,
		VoucherState:      1,
		VoucherActiveDate: time.Now().Unix(),
		VoucherOwnerId:    member.MemberId,
		VoucherOwnerName:  member.MemberName,
		VoucherTType:      tpl.VoucherTType,
		VoucherTGcId:      tpl.VoucherTGcId,
		VoucherSpecialId:  tpl.VoucherSpecialId,
		VoucherBrandId:    tpl.VoucherBrandId,
		VoucherFrom:       7, // 0 默认 1 子龙办卡派送 2预约 3新用户，4老用户，5支付成功赠送 6 答题活动 7 双旦活动抽奖
	}
	voucher.VoucherStartDate, voucher.VoucherEndDate = getTemplateLimitTimestamp(tpl)
	return voucher
}

// 生成代金券编码，参考php生成规则
func generateVoucherCode(MemberId int32, i int) string {
	rand.Seed(time.Now().UnixNano())
	str1 := fmt.Sprintf("%02d", rand.Intn(89)+10)

	str2 := fmt.Sprintf("%03d", time.Now().UnixNano()/1000)
	str2 = str2[len(str2)-3:]

	str3 := fmt.Sprintf("%010d", time.Now().Unix()-946656000)
	str4 := fmt.Sprintf("%v%02d", i, MemberId%1000)

	return str1 + str2 + str3 + str4
}

// 获取模板时间戳
func getTemplateLimitTimestamp(tpl *models.UpetVoucherTemplate) (start int64, end int64) {
	if tpl.VoucherDays < 1 {
		return tpl.VoucherTStartDate, tpl.VoucherTEndDate
	}
	start = time.Now().AddDate(0, 0, int(tpl.VoucherStartDay-1)).Unix()

	y, m, d := time.Now().AddDate(0, 0, int(tpl.VoucherStartDay+tpl.VoucherDays-1)).Date()
	end = time.Date(y, m, d, 0, 0, -1, 0, time.Now().Location()).Unix()
	return
}
