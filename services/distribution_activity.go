package services

import (
	"_/helpers"
	"_/models"
	"_/proto/sh"
	"_/utils"
	"bytes"
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
)

type DistributionActivity struct {
	DistributionCommon
}

// 限时活动列表
func (da *DistributionActivity) DisLimitActivityList(ctx context.Context, in *sh.DisLimitActivityListRequest) (out *sh.DisLimitActivityListResponse, err error) {
	out = new(sh.DisLimitActivityListResponse)
	session := models.GetDBConn().NewSession()
	defer session.Close()
	session.Where("store_id=?", in.OrgId)
	// A、搜索条件处理
	if in.Status > 0 {
		session.Where("status = ?", in.Status)
	}
	if len(in.SearchName) > 0 {
		switch in.SearchType {
		case 1:
			session.Where("name like '%" + in.SearchName + "%'")
		case 2:
			session.Where("id = ?", cast.ToInt(in.SearchName))
		default:
			out.Code = 400
			out.Message = "未知搜索类型"
			return
		}
	}

	// B、分页数据查询
	var activityList []models.UpetDisCommissionActivity
	sessionCount := session.Clone()
	if err = session.OrderBy("id desc").Limit(int(in.PageSize), int((in.Page-1)*in.PageSize)).Find(&activityList); err != nil {
		out.Message = "出错了啦，" + err.Error()
		out.Code = 400
		return
	}
	if out.Total, err = sessionCount.Count(&models.UpetDisCommissionActivity{}); err != nil {
		out.Message = "总数查询出错了啦，" + err.Error()
		out.Code = 400
		return
	}

	// C、搜索结果处理
	if len(activityList) > 0 {
		for k, _ := range activityList {
			out.Data = append(out.Data, &sh.DisLimitActivityListData{
				ActivityId: activityList[k].Id,
				Name:       activityList[k].Name,
				LastEditor: activityList[k].LastEditor,
				StartTime:  activityList[k].StartTime.Format(MinuteLayout),
				EndTime:    activityList[k].EndTime.Format(MinuteLayout),
				Status:     da.getStatusWord(activityList[k].Status),
				StatusCode: activityList[k].Status,
			})
		}
	}

	out.Code = 200
	return
}

// 限时佣金活动新增、修改
func (da *DistributionActivity) DisLimitActivityOperate(ctx context.Context, in *sh.DisLimitActivityOperateRequest) (out *sh.DisResponse, err error) {
	out = new(sh.DisResponse)
	out.Code = 400

	// A、防止重复提交
	if da.isRepeatSubmit(in) {
		out.Message = "请勿重复操作"
		return
	}

	// B、时间参数校验，前端传递精确到分
	var sTime, eTime time.Time
	if sTime, err = time.ParseInLocation(kit.DATETIME_LAYOUT, in.StartTime+":00", time.Local); err != nil {
		out.Message = "开始时间格式错误"
		return
	}
	if eTime, err = time.ParseInLocation(kit.DATETIME_LAYOUT, in.EndTime+":00", time.Local); err != nil {
		out.Message = "结束时间格式错误"
		return
	}

	// C、存储数据拼接
	ctxInfo := utils.LoadGrpcContext(ctx) // 登录用户信息
	disCommissionActivity := models.UpetDisCommissionActivity{
		StoreId:    in.OrgId,
		Name:       in.Name,
		StartTime:  sTime,
		EndTime:    eTime,
		UpdatedAt:  time.Now(),
		LastEditor: ctxInfo.UserInfo.UserName,
	}

	var activityId int64 = 0
	logContent := ""
	// D、更新或新增逻辑处理
	if in.ActivityId > 0 {
		// 修改
		if logContent, err = da.editLimitActivity(disCommissionActivity, in); err != nil {
			return
		}
		activityId = in.ActivityId
	} else {
		// 新增
		if activityId, err = da.addLimitActivity(disCommissionActivity, in); err != nil {
			return
		}
		logContent = "活动新增，名称：" + in.Name
	}

	// D、操作日志
	da.logAdd(ctx, models.UpetDisGoodsLog{
		ActivityId: activityId,
		Content:    logContent,
	})

	out.Code = 200
	out.Message = "操作成功！"
	return
}

// / 添加活动
func (da *DistributionActivity) addLimitActivity(disCommissionActivity models.UpetDisCommissionActivity, in *sh.DisLimitActivityOperateRequest) (activityId int64, err error) {

	// 条件判断
	if in.StartTime >= in.EndTime {
		return activityId, errors.New("结束时间必须大于开始时间")
	}
	if in.StartTime <= time.Now().String() {
		return activityId, errors.New("开始时间必须大于当前时间")
	}

	// 数据存储
	db := models.GetDBConn()
	disCommissionActivity.Status = LimitActivityUnStart
	disCommissionActivity.CreatedAt = time.Now()
	if _, err = db.Insert(&disCommissionActivity); err != nil {
		return activityId, errors.New("新增失败，请稍后再试。" + err.Error())
	}
	activityId = disCommissionActivity.Id

	return
}

// 修改活动
func (da *DistributionActivity) editLimitActivity(disCommissionActivity models.UpetDisCommissionActivity, in *sh.DisLimitActivityOperateRequest) (logContent string, err error) {
	session := models.GetDBConn().NewSession()
	defer session.Close()
	// A、获取原有活动信息
	record, err := models.GetCommissionActivity(in.ActivityId)
	if err != nil {
		return
	}

	// B、条件判断
	if record.Id <= 0 {
		return logContent, errors.New("活动不存在")
	}
	if in.EndTime <= time.Now().String() {
		return logContent, errors.New("结束时间必须大于当前时间")
	}
	switch record.Status {
	case LimitActivityDoing:
		if record.StartTime.Format(MinuteLayout) != in.StartTime {
			return logContent, errors.New("进行中的活动，开始时间不允许修改！")
		}
	case LimitActivityUnStart:
		if in.StartTime >= in.EndTime {
			return logContent, errors.New("结束时间必须大于开始时间")
		}
		if in.StartTime <= time.Now().String() {
			return logContent, errors.New("开始时间必须大于当前时间")
		}
	case LimitActivityFinish:
		return logContent, errors.New("结束的活动不允许编辑")
	case LimitActivityStop:
		return logContent, errors.New("失效活动不允许编辑")
	default:
		return logContent, errors.New("活动状态异常，无法修改")
	}
	// 修改时间，需要判断已经加入的商品是否与其他活动中的商品时间有重叠
	if record.Status == LimitActivityDoing || record.Status == LimitActivityUnStart {
		var skuIds []string
		if skuIds, err = models.GetCommissionActivitySkuIds(session, in.ActivityId); err != nil {
			return
		}
		if len(skuIds) > 0 {
			var total int64
			total, err = models.CheckGoodsInOtherActivity(in.ActivityId, skuIds, disCommissionActivity.StartTime, disCommissionActivity.EndTime, in.OrgId)
			if err != nil {
				return
			}
			if total > 0 {
				return logContent, errors.New("活动中有商品在该时间段内参加了其他活动")
			}
		}
	}

	// C、活动修改
	if _, err = session.ID(in.ActivityId).Update(&disCommissionActivity); err != nil {
		return logContent, errors.New("修改失败,请稍后再试。" + err.Error())
	}

	// D、操作日志
	logContent = "活动编辑，"
	if record.Name != in.Name {
		logContent = logContent + "活动名称" + record.Name + "->" + in.Name + "，"
	}
	if record.StartTime.Format(MinuteLayout) != in.StartTime {
		logContent = logContent + "开始时间" + record.StartTime.Format(MinuteLayout) + "->" + in.StartTime + "，"
	}
	if record.EndTime.Format(MinuteLayout) != in.EndTime {
		logContent = logContent + "结束时间" + record.EndTime.Format(MinuteLayout) + "->" + in.EndTime + "，"
	}
	logContent = strings.TrimRight(logContent, "，")

	return
}

// 限时佣金/活动失效
func (da *DistributionActivity) DisLimitActivityStop(ctx context.Context, in *sh.DisLimitActivityStopRequest) (out *sh.DisResponse, err error) {
	out = new(sh.DisResponse)
	out.Code = 400
	db := models.GetDBConn()

	// A、活动状态判断
	record, err := models.GetCommissionActivity(in.ActivityId)
	if err != nil {
		return
	}
	if record.Status == LimitActivityStop {
		out.Message = "活动已是失效状态！"
		return
	}
	if record.Status != LimitActivityDoing {
		out.Message = "非进行中的活动无法失效！"
		return
	}

	session := db.NewSession()
	defer session.Close()
	session.Begin()
	// B、更新活动状态
	ctxInfo := utils.LoadGrpcContext(ctx) // 登录用户信息
	if _, err = session.ID(in.ActivityId).Update(&models.UpetDisCommissionActivity{
		Status:     LimitActivityStop,
		LastEditor: ctxInfo.UserInfo.UserName,
		UpdatedAt:  time.Now(),
	}); err != nil {
		session.Rollback()
		out.Message = "活动失效失败，请稍后再试！" + err.Error()
		return
	}

	// C、活动中所有商品佣金需要还原，更新upet_goods表
	skuIds, err := models.GetCommissionActivitySkuIds(session, in.ActivityId)
	if err != nil {
		session.Rollback()
		out.Message = "活动下商品查询失败，" + err.Error()
		return
	}
	if len(skuIds) > 0 {
		_, err = session.Exec("update `upet_goods` set dis_activity_id = 0,dis_commis_rate = dis_normal_commis_rate WHERE goods_id IN ("+strings.Join(skuIds, ",")+") AND store_id=?", in.OrgId)
		if err != nil {
			session.Rollback()
			out.Message = "sku" + strings.Join(skuIds, ",") + "设置失败，" + err.Error()
			return
		}
		// 单个商品操作日志
		var goodsData []*models.UpetGoods
		if err = session.Select("goods_id,goods_commonid").In("goods_id", skuIds).Where("store_id=?", in.OrgId).Find(&goodsData); err != nil {
			session.Rollback()
			out.Message = "sku" + strings.Join(skuIds, ",") + "商品查询失败，" + err.Error()
			return
		}
		goodsCommonIds := make(map[string]int, len(goodsData))
		for k, _ := range goodsData {
			goodsCommonIds[cast.ToString(goodsData[k].GoodsId)] = goodsData[k].GoodsCommonid
		}
		for _, skuId := range skuIds {
			goodsCommonId := goodsCommonIds[skuId]
			da.logAdd(ctx, models.UpetDisGoodsLog{
				ActivityId: in.ActivityId,
				Content:    "活动商品失效，商品id->" + cast.ToString(skuId),
				Sku:        cast.ToInt(skuId),
				Spu:        goodsCommonId,
			})
		}
	}
	session.Commit()

	// D、操作日志
	da.logAdd(ctx, models.UpetDisGoodsLog{
		ActivityId: in.ActivityId,
		Content:    "活动失效",
	})

	out.Code = 200
	out.Message = "操作成功!"
	return
}

// 限时佣金/活动分销商品列表
func (da *DistributionActivity) DisLimitActivityGoodsList(ctx context.Context, in *sh.DisLimitActivityGoodsListRequest) (out *sh.DisLimitActivityGoodsListResponse, err error) {
	out = new(sh.DisLimitActivityGoodsListResponse)
	out.Code = 400
	db := models.GetDBConn()

	// A、活动信息
	var activity models.UpetDisCommissionActivity
	if activity, err = models.GetCommissionActivity(in.ActivityId); err != nil {
		return
	}

	out.ActivityInfo = &sh.DisLimitActivityListData{
		ActivityId: in.ActivityId,
		Status:     da.getStatusWord(activity.Status),
		StatusCode: activity.Status,
		StartTime:  activity.StartTime.Format(MinuteLayout),
		EndTime:    activity.EndTime.Format(MinuteLayout),
		LastEditor: activity.LastEditor,
		Name:       activity.Name,
	}

	// B、活动关联的商品信息，搜索条件处理
	var conditionStr strings.Builder
	conditionStr.WriteString("  a.status = 1 AND a.activity_id = " + cast.ToString(in.ActivityId))
	if len(in.SearchName) > 0 {
		switch in.SearchType {
		case 1:
			conditionStr.WriteString(" AND b.goods_name like '%" + in.SearchName + "%'")
		case 2:
			conditionStr.WriteString(" AND a.sku_id = " + cast.ToString(cast.ToInt(in.SearchName)))
		default:
			out.Message = "未知搜索类型"
			return
		}
	}
	// 分销上架商品条件
	conditionStr.WriteString(fmt.Sprintf(" AND b.is_dis = 1 AND b.goods_state = 1 AND b.store_id=%d ", in.OrgId))

	// C、分页和统计查询
	sql := "SELECT a.store_id,a.sku_id,a.activity_rate as activity_commission,b.goods_name,b.goods_image as pic,IF(b.dis_normal_commis_rate > 0,b.dis_normal_commis_rate,b.dis_commis_rate) normal_commission FROM `upet_dis_commission_activity_goods` a LEFT JOIN `upet_goods` b ON a.sku_id = b.goods_id and a.store_id=b.store_id WHERE " +
		conditionStr.String() + "  order by a.id desc limit ?,?"
	if err = db.SQL(sql, in.PageSize*in.Page-in.PageSize, in.PageSize).Find(&out.Data); err != nil {
		out.Message = "查询失败，" + err.Error()
		return
	}
	sqlCount := "SELECT count(a.id) as total FROM `upet_dis_commission_activity_goods` a LEFT JOIN `upet_goods` b ON a.sku_id = b.goods_id and a.store_id=b.store_id WHERE " + conditionStr.String()
	db.SQL(sqlCount).Get(&out.Total)

	// 图片处理
	if len(out.Data) > 0 {
		for k, _ := range out.Data {
			out.Data[k].Pic = helpers.GetGoodsListUrl(out.Data[k].Pic)
			out.Data[k].ShopName = ""
			if out.Data[k].StoreId == 3 {
				out.Data[k].ShopName = "润合云店"
			} else if out.Data[k].StoreId == 4 {
				out.Data[k].ShopName = "百林康源"
			}
		}
	}

	out.Code = 200
	out.Message = "查询成功！"
	return
}

// 活动商品删除
func (da *DistributionActivity) DisLimitActivityGoodsDelete(ctx context.Context, in *sh.DisLimitActivityGoodsDeleteRequest) (out *sh.DisResponse, err error) {
	out = new(sh.DisResponse)
	out.Code = 400
	db := models.GetDBConn()
	session := db.NewSession()
	defer session.Close()

	// A、条件判断
	if da.isRepeatSubmit(in) {
		out.Message = "请勿重复操作"
		return
	}
	if in.ActivityId < 1 || in.SkuId < 1 {
		out.Message = "参数错误！"
		return
	}
	var activity models.UpetDisCommissionActivity
	if activity, err = models.GetCommissionActivity(in.ActivityId); err != nil {
		out.Message = "活动不存在！"
		return
	}
	var record models.UpetDisCommissionActivityGoods
	if _, err = session.Where("activity_id = ? and sku_id = ? and status = 1 and store_id=?", in.ActivityId, in.SkuId, in.OrgId).Get(&record); err != nil {
		return
	}
	if record.Id < 1 {
		out.Message = "活动商品已删除或不存在！"
		return
	}

	// B、数据更新
	session.Begin()
	if _, err = session.ID(record.Id).Update(&models.UpetDisCommissionActivityGoods{
		Status:    2,
		UpdatedAt: time.Now(),
	}); err != nil {
		out.Message = "删除失败，稍后再试！" + err.Error()
		return
	}
	// 如果活动是进行中，删除需要还原goods表佣金值
	var goods models.UpetGoods
	session.Select("goods_id,goods_commonid,dis_commis_rate,dis_normal_commis_rate").Where("goods_id = ? AND store_id=?", in.SkuId, in.OrgId).Get(&goods)
	if activity.Status == LimitActivityDoing {
		updateG := models.UpetGoods{
			DisActivityId: 0,
			DisCommisRate: goods.DisNormalCommisRate,
		}
		if _, err = session.Where("goods_id = ? AND store_id = ?", in.SkuId, in.OrgId).Cols("dis_commis_rate,dis_activity_id").Update(&updateG); err != nil {
			session.Rollback()
			out.Message = "sku" + cast.ToString(in.SkuId) + "设置失败，" + err.Error()
			return
		}
	}
	session.Commit()

	// C、操作日志
	da.logAdd(ctx, models.UpetDisGoodsLog{
		ActivityId: in.ActivityId,
		Content:    "活动商品删除，商品id->" + cast.ToString(in.SkuId),
		Sku:        cast.ToInt(in.SkuId),
		Spu:        goods.GoodsCommonid,
	})

	out.Code = 200
	out.Message = "操作成功！"
	return
}

// 限时佣金/活动商品佣金批量设置，涉及到佣金变化的都会调用
func (da *DistributionActivity) DisLimitActivityCommissionSet(ctx context.Context, in *sh.DisLimitActivityCommissionSetRequest) (out *sh.DisResponse, err error) {
	out = new(sh.DisResponse)
	out.Code = 400
	db := models.GetDBConn()
	logPrefix := fmt.Sprintf("限时佣金/活动商品佣金批量设置======主体：%d", in.OrgId)
	glog.Info(logPrefix, "入参是：", kit.JsonEncode(in))
	// A、防止重复提交
	if da.isRepeatSubmit(in) {
		out.Message = "重复设置"
		return
	}
	if len(in.ActivityCommission) < 1 {
		out.Message = "参数缺失！"
		return
	}

	// B、活动判断
	var activity models.UpetDisCommissionActivity
	if activity, err = models.GetCommissionActivity(in.ActivityId); err != nil {
		return
	}
	if !(activity.Status == LimitActivityDoing || activity.Status == LimitActivityUnStart) {
		out.Message = "活动已结束或已失效，无法添加商品"
		return
	}

	// C、数据处理
	//C-1、先获取所有sku_id，批量查询
	var skuIds []string
	for _, v := range in.ActivityCommission {
		skuIds = append(skuIds, strings.Trim(cast.ToString(v.SkuId), " "))
	}
	glog.Info(logPrefix, "skuids是", kit.JsonEncode(skuIds))
	// C-2、以下都是条件判断逻辑
	// 添加商品时校验商品是否在别的有时间交叉的未生效和已生效活动里，且商品是分销商品
	total, err := models.CheckGoodsInOtherActivity(in.ActivityId, skuIds, activity.StartTime, activity.EndTime, in.OrgId)
	if err != nil {
		out.Message = err.Error()
		return
	}
	if total > 0 {
		out.Message = "设置失败，分销商品有参与其他活动"
		return
	}

	// 传入的数据组装k-v
	commissions := make(map[int]float32, len(in.ActivityCommission))
	for k, _ := range in.ActivityCommission {
		if in.ActivityCommission[k].ActivityCommission < 0.5 || in.ActivityCommission[k].ActivityCommission > 50 {
			out.Message = "sku" + cast.ToString(in.ActivityCommission[k].SkuId) + "活动佣金必须在0.5%-50%"
			return
		}
		commissions[cast.ToInt(in.ActivityCommission[k].SkuId)] = in.ActivityCommission[k].ActivityCommission
	}

	// 批量查询商品，判断商品状态和佣金
	var goodsData []*models.UpetGoods
	if err = db.Select("goods_id,dis_commis_rate,dis_normal_commis_rate,is_dis,goods_state,goods_commonid").Where("store_id=?", in.OrgId).In("goods_id", skuIds).Find(&goodsData); err != nil {
		glog.Infof("%s=批量查询商品=发生错误：%s", logPrefix, err.Error())
		return
	}
	if len(goodsData) < 1 {
		out.Message = "设置失败，未查询到商品信息"
		return
	}
	goodsInfo := make(map[int64]*models.UpetGoods, len(goodsData)) // 商品信息
	for k, _ := range goodsData {
		if goodsData[k].GoodsState != 1 {
			out.Message = "sku" + cast.ToString(goodsData[k].GoodsId) + "已下架"
			return
		}
		if goodsData[k].IsDis != 1 {
			out.Message = "sku" + cast.ToString(goodsData[k].GoodsId) + "不是分销商品"
			return
		}
		if has, ok := commissions[goodsData[k].GoodsId]; ok {
			if goodsData[k].DisNormalCommisRate > 0 {
				if goodsData[k].DisNormalCommisRate == has {
					out.Message = "sku" + cast.ToString(goodsData[k].GoodsId) + "活动佣金和日常佣金不能相等"
					return
				}
			} else {
				if has == goodsData[k].DisCommisRate {
					out.Message = "sku" + cast.ToString(goodsData[k].GoodsId) + "活动佣金和日常佣金不能相等"
					return
				}
			}
		}
		goodsInfo[cast.ToInt64(goodsData[k].GoodsId)] = goodsData[k]
	}

	// C-3、批量插入
	var activityGoods []*models.UpetDisCommissionActivityGoods
	if err = db.Where("status = ? AND activity_id = ?", 1, in.ActivityId).In("sku_id", skuIds).Find(&activityGoods); err != nil {
		out.Message = "活动商品查询异常，" + err.Error()
		return
	}
	aGoods := make(map[int64]int64, len(activityGoods))
	if len(activityGoods) > 0 {
		for _, a := range activityGoods {
			aGoods[a.SkuId] = a.Id
		}
	}

	// 开启事务
	session := db.NewSession()
	defer session.Close()
	if err = session.Begin(); err != nil {
		return
	}
	for _, v := range in.ActivityCommission {
		g, ok := goodsInfo[v.SkuId]
		if !ok {
			out.Message = "sku" + cast.ToString(v.SkuId) + "商品信息不存在！"
			return
		}

		if activityId, ok := aGoods[v.SkuId]; ok {
			// 更新
			if _, err = session.ID(activityId).Update(&models.UpetDisCommissionActivityGoods{
				ActivityRate: v.ActivityCommission,
				UpdatedAt:    time.Now(),
			}); err != nil {
				session.Rollback()
				out.Message = "sku" + cast.ToString(v.SkuId) + "设置失败，" + err.Error()
				return
			}
		} else {
			// 插入
			if _, err = session.Insert(&models.UpetDisCommissionActivityGoods{
				SkuId:        v.SkuId,
				StoreId:      in.OrgId,
				Status:       1,
				ActivityId:   in.ActivityId,
				ActivityRate: v.ActivityCommission,
				CreatedAt:    time.Now(),
				UpdatedAt:    time.Now(),
			}); err != nil {
				session.Rollback()
				out.Message = "sku" + cast.ToString(v.SkuId) + "设置失败，" + err.Error()
				return
			}
		}

		// 如果活动是进行中，则需要更新goods表佣金值
		if activity.Status == LimitActivityDoing {
			updateG := models.UpetGoods{
				DisActivityId: cast.ToInt(activity.Id),
				DisCommisRate: v.ActivityCommission,
			}
			if g.DisNormalCommisRate <= 0.01 {
				updateG.DisNormalCommisRate = g.DisCommisRate
			}
			if _, err = session.Where("goods_id = ? AND store_id=?", g.GoodsId, in.OrgId).Update(&updateG); err != nil {
				session.Rollback()
				out.Message = "sku" + cast.ToString(v.SkuId) + "设置失败，" + err.Error()
				return
			}
		}

		// 操作日志
		go func(ac *sh.DisLimitActivityCommissionData, g *models.UpetGoods) {
			disGoodsLog := models.UpetDisGoodsLog{
				ActivityId: in.ActivityId,
				Content:    "活动佣金设置" + cast.ToString(v.SkuId) + "," + cast.ToString(g.DisCommisRate) + "->" + cast.ToString(ac.ActivityCommission),
				Sku:        cast.ToInt(ac.SkuId),
			}
			// 仅活动进行中记录 spu数据，避免spu日志展示无效数据
			if activity.Status == LimitActivityDoing {
				disGoodsLog.Spu = g.GoodsCommonid
			}
			da.logAdd(ctx, disGoodsLog)
		}(v, g)
	}
	if err = session.Commit(); err != nil {
		return
	}

	out.Code = 200
	out.Message = "设置成功"
	return
}

// 限时佣金/活动日志
func (da *DistributionActivity) DisLimitActivityLog(ctx context.Context, in *sh.DisLimitActivityLogRequest) (out *sh.DisSpuLogsResponse, err error) {
	out = new(sh.DisSpuLogsResponse)

	db := models.GetDBConn()
	err = db.SQL("SELECT * FROM `upet_dis_goods_log` WHERE activity_id = ? ORDER BY id DESC LIMIT ?,?",
		in.ActivityId, in.PageSize*in.Page-in.PageSize, in.PageSize).Find(&out.Data)
	if err != nil {
		out.Code = 400
		out.Message = "查询失败，" + err.Error()
		return
	}
	db.SQL("SELECT count(*) as total  FROM `upet_dis_goods_log` WHERE activity_id = ?", in.ActivityId).Get(&out.Total)

	out.Code = 200
	return
}

// 限时佣金/商品限时佣金批量导入
func (da *DistributionActivity) DisLimitActivityGoodsImport(ctx context.Context, in *sh.DisLimitActivityGoodsImportRequest) (out *sh.DisResponse, err error) {
	out = new(sh.DisResponse)
	out.Code = 400
	// A、活动判断
	var activity models.UpetDisCommissionActivity
	if activity, err = models.GetCommissionActivity(in.ActivityId); err != nil {
		return
	}
	if !(activity.Status == LimitActivityDoing || activity.Status == LimitActivityUnStart) {
		out.Message = "活动已结束或已失效"
		return
	}

	// B、excel文件接收
	f, err := excelize.OpenReader(bytes.NewReader(in.File))
	if err != nil {
		out.Message = "读取文件出错 " + err.Error()
		return
	}
	defer f.Close()

	// C、批量导入
	err, importInfo := CommissionImport(ctx, f, in.ActivityId, in.OrgId)
	if err != nil {
		out.Message = err.Error()
		return
	}

	// D、添加导入日志
	da.logImportAdd(ctx, models.UpetDisGoodsImport{
		Type:      2,
		TypeId:    cast.ToInt(in.ActivityId),
		Result:    importInfo.ResultContent,
		ResultUrl: importInfo.ResultUrl,
	})

	out.Code = 200
	out.Message = "操作成功！"
	return
}
