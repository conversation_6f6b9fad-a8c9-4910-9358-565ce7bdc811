module _

go 1.13

require (
	github.com/denisenkom/go-mssqldb v0.0.0-20200428022330-06a60b6afbbc // indirect
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/go-sql-driver/mysql v1.5.0
	github.com/go-xorm/xorm v0.7.9
	github.com/golang/protobuf v1.4.2
	github.com/google/uuid v1.1.2
	github.com/labstack/echo/v4 v4.1.16
	github.com/lib/pq v1.7.0 // indirect
	github.com/limitedlee/microservice v0.1.0
	github.com/mattn/go-sqlite3 v1.14.0 // indirect
	github.com/maybgit/glog v0.0.0-20210928064228-9506732eb074
	github.com/maybgit/pbgo v0.0.0-20200601050928-85c4ece4a248
	github.com/olivere/elastic/v7 v7.0.20
	github.com/ppkg/kit v0.0.0-20210928070906-2e2b70f489af
	github.com/robfig/cron/v3 v3.0.1
	github.com/shopspring/decimal v1.2.0
	github.com/spf13/cast v1.3.1
	github.com/streadway/amqp v0.0.0-20200108173154-1c71cc93ed71
	github.com/tricobbler/rp-kit v0.0.0-20210413075252-45df7834f17a
	github.com/xuri/excelize/v2 v2.5.0
	google.golang.org/genproto v0.0.0-20200526211855-cb27e3aa2013
	google.golang.org/grpc v1.28.0
	google.golang.org/protobuf v1.25.0
	xorm.io/builder v0.3.7 // indirect
)

replace _ => ./
