package helpers

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// GetGoodsListUrl 获取商品列表图片完整链接
func GetGoodsListUrl(path string) string {
	if strings.HasPrefix(path, "http") {
		return path
	}
	api := config.GetString("ECUrl")
	parse, err := url.Parse(api)
	if err != nil {
		return path
	}
	envString := strings.Split(parse.Host, ".")[0]
	return fmt.Sprintf("https://oss.upetmart.com/%s/shop/store/goods/1/%s?x-oss-process=style/product-240", envString, path)
}

type PaginateReq struct {
	Count    *xorm.Session
	List     *xorm.Session
	Page     int32
	PageSize int32
}

// Paginate 分页查询
func (in *PaginateReq) Paginate(total *int32, data interface{}) error {
	if count, err := in.Count.Count(); err != nil {
		return errors.New("查询Count出错 " + err.Error())
	} else {
		*total = int32(count)
	}

	if in.Page < 1 {
		in.Page = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}
	in.List.Limit(int(in.PageSize), int(in.PageSize*(in.Page-1)))
	if err := in.List.Find(data); err != nil {
		return errors.New("查询List出错 " + err.Error())
	}

	return nil
}

// UploadFile 上传文件需要的信息
// 文件路径方法 reader, err := os.Open(fileName)
// excel方式 b,err := file.WriteToBuffer();reader = bytes.NewReader(b.Bytes())
type UploadFile struct {
	Name   string
	Reader io.Reader
}

// UploadQiNiuResponse 上传到七牛响应
type UploadQiNiuResponse struct {
	FileName string
	Size     int
	Url      string
	Error    string
}

// ToQiNiu 上传文件到七牛云
func (uf *UploadFile) ToQiNiu() (url string, err error) {
	if len(uf.Name) < 1 {
		return "", errors.New("文件名称不能为空")
	}

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, _ := writer.CreateFormFile("file", uf.Name)
	if _, err = io.Copy(part, uf.Reader); err != nil {
		return
	}
	if err = writer.Close(); err != nil {
		return
	}

	host := config.GetString("file-upload-url")
	if len(host) == 0 {
		host = "https://api.rp-pet.com"
	}
	httpResp, err := http.Post(host+"/fss/newup", writer.FormDataContentType(), body)
	if err != nil {
		return
	}
	defer httpResp.Body.Close()

	resBody, err := ioutil.ReadAll(httpResp.Body)
	if err != nil {
		return
	}

	res := new(UploadQiNiuResponse)
	if err = json.Unmarshal(resBody, res); err != nil {
		return "", errors.New("解析响应body出错 " + err.Error())
	}
	if httpResp.StatusCode >= 400 {
		if len(res.Error) == 0 {
			res.Error = httpResp.Status
		}
		return "", errors.New("请求出错 " + res.Error)
	}

	res.Url = strings.Replace(res.Url, "http://", "https://", 1)

	return res.Url, nil
}

// UploadExcelToQiNiu 上传excel文件到七牛云
func UploadExcelToQiNiu(file *excelize.File, name string) (url string, err error) {
	b, err := file.WriteToBuffer()
	if err != nil {
		return
	}
	if len(name) < 1 {
		name = kit.GetGuid36() + ".xlsx"
	}
	uf := &UploadFile{
		Name:   name,
		Reader: bytes.NewReader(b.Bytes()),
	}

	return uf.ToQiNiu()
}

// TimestampToDateTime 时间戳转日期时间
func TimestampToDateTime(timestamp int32) string {
	if timestamp == 0 {
		return ""
	}
	return time.Unix(int64(timestamp), 0).In(time.Local).Format(kit.DATETIME_LAYOUT)
}

// TimestampToDate 时间戳转日期
func TimestampToDate(timestamp int32) string {
	if timestamp == 0 {
		return ""
	}
	return time.Unix(int64(timestamp), 0).In(time.Local).Format(kit.DATE_LAYOUT)
}
