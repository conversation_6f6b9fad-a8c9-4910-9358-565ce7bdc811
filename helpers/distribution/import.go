package distribution

import (
	"_/helpers"
	"_/models"
	"_/proto/common"
	"_/proto/ic"
	"context"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
)

const (
	// whereIn一次限制
	inLimit = 3000
	// 一次最多插入数据
	maxInsert = 1000
	// 最多导入的行数
	maxRows = 50000

	SeckillKey = "stock:seckill:%d:%d"
)

// 上传的文件解析
type fileData struct {
	SheetName string         // 当前工作表名
	Data      [][]string     // 文件内容，除表头
	SkuIds    []string       // 所有SkuId
	Times     map[string]int // 每个sku出现的次数
}

// 导入文件每行结果
type rowResult struct {
	Sku        *models.UpetGoods
	CommisRate float32
	Write      string
	State      int    // 导入状态，0失败，1成功
	FailReason string // 失败原因
}

// 秒杀商品导入文件每行结果
type RowSeckillResult struct {
	Sku        *models.UpetGoods
	GoodsPrice float32
	Stock      int
	State      int    // 导入状态，0失败，1成功
	FailReason string // 失败原因
}

// 参与活动互斥时间
type nMutualTime struct {
	BeginDate     time.Time
	EndDate       time.Time
	PromotionType int32
}

type giftField struct {
	GiftId int32
}

// Import 分销商品批量导入
func Import(username string, file *excelize.File, importType int32, orgId int32) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("批量导入异常 %s", e)
		}
	}()

	fd, err := getFileData(file)
	if err != nil {
		return err
	} else if len(fd.SkuIds) == 0 {
		return errors.New("上传的文件不存在数据")
	}

	skus, err := sliceQuerySkus(fd.SkuIds, orgId)
	if err != nil {
		return err
	}

	session := models.GetDBConn().NewSession()
	defer session.Close()
	_ = session.Begin()

	var (
		logs                    []*models.UpetDisGoodsLog
		skuImport               *models.UpetDisGoodsImport
		successCount, failCount int
	)

	if skuImport, err = insertImport(session, username, importType); err != nil {
		return err
	}
	writer, _ := file.NewStreamWriter(fd.SheetName)
	_ = writer.SetRow("A1", []interface{}{
		"SKU", "佣金", "推广文案", "是否成功", "原因", "导入前日常佣金", "导入前文案", "执行日志",
	})

	// v[]string SKU、佣金、推广文案
	for i, v := range fd.Data {
		r, log := updateSkuByRow(session, v, skus, fd.Times[v[0]], orgId)
		if r.State == 0 {
			failCount++
			_ = writer.SetRow("A"+cast.ToString(i+2), []interface{}{
				v[0], v[1], v[2], "否", r.FailReason, "-", "-", log,
			})
			continue
		}
		successCount++
		_ = writer.SetRow("A"+cast.ToString(i+2), []interface{}{
			v[0], v[1], v[2], "是", r.FailReason, r.Sku.DisNormalCommisRate, r.Sku.DisWrite, log,
		})
		// 存在日志内容，则记录
		if len(log) > 0 {
			logs = append(logs, &models.UpetDisGoodsLog{
				ImportId:  skuImport.Id,
				UserName:  username,
				Spu:       r.Sku.GoodsCommonid,
				Sku:       r.Sku.GoodsId,
				Content:   "批量导入商品" + cast.ToString(r.Sku.GoodsId) + "，" + log,
				CreatedAt: time.Now().Local(),
			})
		}
	}

	if err = writer.Flush(); err != nil {
		return
	}
	// 上传excel文件
	if skuImport.ResultUrl, err = helpers.UploadExcelToQiNiu(file, ""); err != nil {
		return
	}
	if err = updateImportResult(session, skuImport, successCount, failCount); err != nil {
		return
	}
	if err = sliceInsertLogs(session, logs); err != nil {
		return
	}

	// 所有操作成功后提交事务
	if err = session.Commit(); err != nil {
		return errors.New("提交事务出错 " + err.Error())
	}

	return
}

// 获取excel文件内容
func getFileData(file *excelize.File) (fd *fileData, err error) {
	fd = &fileData{
		Times:     make(map[string]int),
		SheetName: file.GetSheetName(0), // 只读取第一个工作表
	}

	rows, err := file.Rows(fd.SheetName)
	if err != nil {
		return fd, errors.New("获取行数据出错 " + err.Error())
	}

	for i := 0; rows.Next(); i++ {
		// 注意这里一定要读取行，不然内容会附加到下一行
		row, err := rows.Columns()
		// 表头不处理
		if i == 0 {
			continue
		}
		if i > maxRows {
			return fd, errors.New("最多导入5万行数据")
		}
		if err != nil {
			return fd, errors.New("获取列数据出错 " + err.Error())
		}
		for k, v := range row {
			row[k] = strings.TrimSpace(v)
		}

		// 无效的空数忽略
		if len(row) == 0 || len(row[0]) < 1 {
			continue
		}
		if len(row) == 1 {
			row = append(row, "", "")
		} else if len(row) == 2 {
			row = append(row, "")
		}

		fd.Data = append(fd.Data, row)
		fd.SkuIds = append(fd.SkuIds, row[0])
		// 记录单个sku出现的次数，超过1次数据无效
		fd.Times[row[0]] = fd.Times[row[0]] + 1
	}

	_ = rows.Close()
	return
}

// 一次性查询商品
func sliceQuerySkus(ids []string, orgId int32) (skus map[int]*models.UpetGoods, err error) {
	db := models.GetDBConn()
	total := len(ids)
	skus = make(map[int]*models.UpetGoods)

	// 分段查询
	for i := 0; i < total; i = i + inLimit {
		end := i + inLimit
		if end > total {
			end = total
		}
		in := ids[i:end]

		var t []*models.UpetGoods
		if err := db.Table("upet_goods").Select("goods_id,goods_commonid,dis_write,is_dis,dis_commis_rate,dis_activity_id,dis_normal_commis_rate,goods_price,goods_promotion_type,have_gift,goods_name,goods_image,is_virtual").
			In("goods_id", in).Where("goods_state = 1 AND store_id=?", orgId).
			OrderBy("goods_id desc").Find(&t); err != nil {
			return nil, errors.New("查询商品出错 " + err.Error())
		}
		for _, s := range t {
			skus[s.GoodsId] = s
		}
	}

	return
}

// 插入导入表
func insertImport(session *xorm.Session, username string, importType int32) (*models.UpetDisGoodsImport, error) {
	m := &models.UpetDisGoodsImport{
		Type:      cast.ToInt(importType),
		UserName:  username,
		CreatedAt: time.Now().Local(),
	}
	if _, err := session.Insert(m); err != nil {
		return nil, errors.New("插入导入表出错 " + err.Error())
	}

	return m, nil
}

// 更新导入结果
func updateImportResult(session *xorm.Session, skuImport *models.UpetDisGoodsImport, successCount, failCount int) error {
	if successCount > 0 {
		if failCount > 0 {
			skuImport.Result = fmt.Sprintf("成功：%v，失败：%v", successCount, failCount)
		} else {
			skuImport.Result = "全部成功：" + cast.ToString(successCount)
		}
	} else {
		skuImport.Result = "全部失败：" + cast.ToString(failCount)
	}

	if _, err := session.ID(skuImport.Id).Update(skuImport); err != nil {
		return errors.New("更新导入结果出错 " + err.Error())
	}

	return nil
}

// 获取单个sku更新结果
func updateSkuByRow(session *xorm.Session, row []string, skus map[int]*models.UpetGoods, count int, orgId int32) (r *rowResult, log string) {
	r = validateRowData(row, skus, count)
	if r.State == 0 {
		return
	}

	// 取消分销
	if r.CommisRate == 0 {
		if err := r.Sku.CancelDis(session, orgId); err != nil {
			r.State = 0
			r.FailReason = "取消分销出错 " + err.Error()
			return
		}
		return r, "取消分销"
	}

	// 如果之前分销商品
	if r.Sku.IsDis > 0 {
		logs := []string{"更新"}
		u := make(map[string]interface{})

		// 存在变动
		if math.Abs(float64(r.Sku.DisNormalCommisRate-r.CommisRate)) > 0.001 {
			u["dis_normal_commis_rate"] = r.CommisRate
			if r.Sku.DisActivityId == 0 {
				u["dis_commis_rate"] = r.CommisRate
			}
			logs = append(logs, fmt.Sprintf("日常佣金 %v%%->%v%%", r.Sku.DisNormalCommisRate, r.CommisRate))
		}
		if len(r.Write) > 0 && r.Sku.DisWrite != r.Write {
			u["dis_write"] = r.Write
			logs = append(logs, fmt.Sprintf("文案 %s->%s", r.Sku.DisWrite, r.Write))
		}

		if len(u) == 0 {
			r.FailReason = "未更新"
			r.State = 1
			return
		}
		if _, err := session.Table("upet_goods").Where("goods_id = ? AND store_id=?", orgId, r.Sku.GoodsId).Update(u); err != nil {
			r.State = 0
			r.FailReason = "更新商品出错 " + err.Error()
			return
		}

		return r, strings.Join(logs, "，")
	}

	// 非分销商品添加
	if err := r.Sku.AddDis(session, r.CommisRate, r.Write, orgId); err != nil {
		r.State = 0
		r.FailReason = err.Error()
		return
	}
	log = fmt.Sprintf("新增分销，日常佣金 %v%%", r.CommisRate)
	if len(r.Write) > 0 {
		log = log + "，文案 " + r.Write
	}

	return
}

// 验证导入的行数据
// row = SKU、佣金、推广文案
func validateRowData(row []string, skus map[int]*models.UpetGoods, count int) (r *rowResult) {
	r = &rowResult{
		Write: row[2],
	}

	skuId := cast.ToInt(row[0])
	if skuId < 1 {
		r.FailReason = "Sku无效"
		return
	}
	if rate, err := cast.ToFloat32E(row[1]); err != nil {
		r.FailReason = "佣金格式错误"
		return
	} else {
		r.CommisRate = rate
		// 佣金等于0表示取消
		if ((rate-0.01) < -0.001 && rate != 0) || (rate-50) > 0.001 {
			r.FailReason = "佣金范围错误"
			return
		}
	}
	if len(r.Write) > 100 {
		r.FailReason = "文案字数限制100字"
		return
	}
	// 多次的算失败
	if count > 1 {
		r.FailReason = "存在重复导入"
		return
	}

	if sku, has := skus[skuId]; !has {
		r.FailReason = "Sku不存在或已下架"
		return
	} else {
		r.Sku = sku
	}
	if r.Sku.IsDis == 0 && r.CommisRate == 0 {
		r.FailReason = "非分销商品不能取消"
		return
	}
	// 日常佣金不存在时等于当前佣金
	if r.Sku.DisNormalCommisRate <= 0 {
		r.Sku.DisNormalCommisRate = r.Sku.DisCommisRate
	}

	r.State = 1
	return
}

// 分段插入操作日志
func sliceInsertLogs(session *xorm.Session, data []*models.UpetDisGoodsLog) error {
	total := len(data)
	for i := 0; i < total; i = i + maxInsert {
		end := i + maxInsert
		if end > total {
			end = total
		}
		in := data[i:end]

		if _, err := session.Insert(in); err != nil {
			return errors.New("插入操作日志出错 " + err.Error())
		}
	}
	return nil
}

// 秒杀商品批量导入
func ImportSeckillProduct(username string, file *excelize.File, importType, id int32, orgId int32) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("批量导入异常 %s", e)
		}
	}()

	fd, err := getFileData(file)
	if err != nil {
		return err
	} else if len(fd.SkuIds) == 0 {
		return errors.New("上传的文件不存在数据")
	}

	skus, err := sliceQuerySkus(fd.SkuIds, orgId)
	if err != nil {
		return err
	}

	session := models.GetDBConn().NewSession()
	defer session.Close()
	_ = session.Begin()

	atSession := models.GetActivityDBConn().NewSession()
	defer atSession.Close()

	var (
		logs                    []*models.UpetDisGoodsLog
		skuImport               *models.UpetDisGoodsImport
		successCount, failCount int
	)

	if skuImport, err = insertImport(session, username, importType); err != nil {
		return err
	}
	writer, _ := file.NewStreamWriter(fd.SheetName)
	_ = writer.SetRow("A1", []interface{}{
		"SKU", "秒杀价", "活动库存", "导入结果", "备注",
	})
	// 秒杀活动时间
	promotion := models.Promotion{}
	_, err = atSession.Table("promotion").Where("id=?", id).Get(&promotion)
	if err != nil {
		return errors.New("获取活动信息失败")
	}

	if promotion.Status > 2 {
		return errors.New("只有未开始、进行中的活动商品才可以编辑 ")
	}
	productsInfo := make([]*ic.ProductsInfo, 0)
	for _, skuid := range fd.SkuIds {
		productsInfo = append(productsInfo, &ic.ProductsInfo{
			Type:         2,
			SkuId:        cast.ToInt32(skuid),
			IsAllVirtual: 0,
		})
	}
	getStockInfoRequest := &ic.GetStockInfoRequest{
		Source:       1,
		IsNeedPull:   0,
		ProductsInfo: productsInfo,
	}
	inventoryClient := ic.GetInventoryServiceClient()
	getStockInfoReponse, err := inventoryClient.RPC.GetStockInfo(context.Background(), getStockInfoRequest)
	if err != nil {
		return errors.New("查询库存接口失败")
	}
	realStockMap := make(map[int32]int32) // sku-stock
	for _, v := range getStockInfoReponse.GoodsInfo.ProductsInfo {
		realStockMap[v.SkuId] = v.Stock
	}
	redisClient := common.GetRedisConn()
	defer redisClient.Close()
	for i, v := range fd.Data {
		r, log := insertSeckillByRow(atSession, v, skus, fd.Times[v[0]], id, &nMutualTime{
			BeginDate: promotion.BeginTime,
			EndDate:   promotion.EndTime,
		}, realStockMap, redisClient)
		if r.State == 0 {
			failCount++
			_ = writer.SetRow("A"+cast.ToString(i+2), []interface{}{
				v[0], v[1], v[2], "失败", r.FailReason,
			})
			continue
		}
		successCount++
		_ = writer.SetRow("A"+cast.ToString(i+2), []interface{}{
			v[0], v[1], v[2], "成功", r.FailReason,
		})
		// 存在日志内容，则记录
		if len(log) > 0 {
			logs = append(logs, &models.UpetDisGoodsLog{
				ImportId:   skuImport.Id,
				ActivityId: int64(id),
				UserName:   username,
				Spu:        r.Sku.GoodsCommonid,
				Sku:        r.Sku.GoodsId,
				Content:    "批量导入秒杀商品" + cast.ToString(r.Sku.GoodsId) + "，" + log,
				CreatedAt:  time.Now().Local(),
			})
		}
	}

	if err = writer.Flush(); err != nil {
		return
	}
	// 上传excel文件
	if skuImport.ResultUrl, err = helpers.UploadExcelToQiNiu(file, ""); err != nil {
		return
	}
	skuImport.TypeId = int(id)
	if err = updateImportResult(session, skuImport, successCount, failCount); err != nil {
		return
	}
	if err = sliceInsertLogs(session, logs); err != nil {
		return
	}

	// 所有操作成功后提交事务
	if err = session.Commit(); err != nil {
		return errors.New("提交事务出错 " + err.Error())
	}

	return
}

// 秒杀活动商品导入逻辑处理 row[] sku 秒杀价 活动库存
func insertSeckillByRow(session *xorm.Session, row []string, skus map[int]*models.UpetGoods, count int, id int32, currentGDate *nMutualTime, realStockMap map[int32]int32, redisClient *redis.Client) (r *RowSeckillResult, log string) {
	r = &RowSeckillResult{}
	//判断库存是否整数
	stock := cast.ToFloat64(row[2])
	if stock != math.Trunc(stock) || stock <= 0 {
		r.State = 0
		r.FailReason = "库存只能正整数"
		return
	}

	//判断库存
	skuId := cast.ToInt32(row[0])
	seckillPrice := cast.ToFloat64(row[1])
	seckillStock := cast.ToInt32(row[2])
	// 判断库存
	if v, has := realStockMap[skuId]; has {
		stock := cast.ToInt32(row[2])
		if v < stock {
			r.State = 0
			r.FailReason = "库存不足"
			return
		}
	} else {
		r.State = 0
		r.FailReason = "查询库存中心失败"
		return
	}

	r = validateSeckillData(row, skus, count, &nMutualTime{
		BeginDate: currentGDate.BeginDate,
		EndDate:   currentGDate.EndDate,
	})
	if r.State == 0 {
		return
	}
	//如果有记录则更新
	promotionProduct := models.PromotionProduct{}
	_, err := session.Where("promotion_id = ? and sku_id = ?", id, skuId).Get(&promotionProduct)
	if err != nil {
		r.FailReason = "获取活动信息失败"
		return
	}
	session.Begin()
	goodsPrice, _ := cast.ToFloat32E(r.Sku.GoodsPrice)
	marketPrice := cast.ToInt32(kit.YuanToFen(goodsPrice))
	// 秒杀活动商品更新
	if promotionProduct.Id > 0 {
		promotionProduct.SeckillPrice = int32(kit.YuanToFen(seckillPrice))
		promotionProduct.SeckillStock = seckillStock
		promotionProduct.MarketPrice = marketPrice
		promotionProduct.ProductName = r.Sku.GoodsName
		promotionProduct.ProductImg = helpers.GetGoodsListUrl(r.Sku.GoodsImage)
		promotionProduct.UpdateTime = time.Now()
		if _, err := session.Where("id = ?", promotionProduct.Id).Update(&promotionProduct); err != nil {
			r.State = 0
			r.FailReason = err.Error()
			return
		}
		log = fmt.Sprintf("更新秒杀活动商品：%v", row[0])
	} else {
		// 秒杀活动商品添加
		seckillPrice := kit.YuanToFen(seckillPrice)
		data := models.PromotionProduct{
			SpuId:        cast.ToInt32(r.Sku.GoodsCommonid),
			SkuId:        skuId,
			PromotionId:  cast.ToInt32(id),
			Types:        11,
			ChannelId:    5,
			ProductName:  r.Sku.GoodsName,
			UpDownState:  1,
			MarketPrice:  marketPrice,
			ProductImg:   helpers.GetGoodsListUrl(r.Sku.GoodsImage),
			SeckillPrice: int32(seckillPrice),
			SeckillStock: seckillStock,
			CreateTime:   time.Now(),
			UpdateTime:   time.Now(),
			StoreId:      1,
		}

		if _, err := session.Insert(&data); err != nil {
			session.Rollback()
			r.State = 0
			r.FailReason = err.Error()
			return
		}
		log = fmt.Sprintf("添加秒杀活动商品：%v", row[0])
	}
	//判断是否已存在记录，有则更新
	var upetData models.UpetPTime
	_, err = models.GetDBConn().Table("upet_p_time").Where("promotion_id =? and goods_id =?", id, skuId).Get(&upetData)
	if err != nil {
		r.FailReason = "获取电商活动信息失败"
		return
	}
	if upetData.LogId > 0 {
		upetData.PromotionPrice = row[1]
		upetData.StartTime = currentGDate.BeginDate.Unix()
		upetData.EndTime = currentGDate.EndDate.Unix()
		_, err := models.GetDBConn().Table("upet_p_time").Where("log_id =?", upetData.LogId).Update(&upetData)
		if err != nil {
			session.Rollback()
			r.FailReason = "添加电商活动信息失败"
			return
		}
	} else {
		upetData := models.UpetPTime{
			StartTime:      currentGDate.BeginDate.Unix(),
			EndTime:        currentGDate.EndDate.Unix(),
			StoreId:        1,
			GoodsId:        cast.ToInt32(row[0]),
			PromotionId:    id,
			PromotionPrice: row[1],
			PromotionType:  models.UpetPTimeSeckill,
		}
		_, err := models.GetDBConn().Table("upet_p_time").Insert(&upetData)
		if err != nil {
			session.Rollback()
			r.FailReason = "添加电商活动信息失败"
			return
		}
	}
	// 将活动虚拟库存写入 redis
	redisKey := fmt.Sprintf(SeckillKey, id, skuId)
	expired := currentGDate.EndDate.Sub(time.Now())
	expired = expired + 1*time.Minute //这里多缓存1分钟

	result, err := redisClient.Set(redisKey, seckillStock, expired).Result()
	if err != nil {
		session.Rollback()
		r.FailReason = "redis库存设置错误"
		return
	}
	getStockFromRedis := redisClient.Get(redisKey).String()
	expireTime := redisClient.TTL(redisKey).String()
	glog.Info("redis-seckillStock=", getStockFromRedis, "，redisKey=", redisKey, "，expireTime=", expireTime, "，result=", result)

	session.Commit()

	return
}

// 秒杀活动商品导入判断：秒杀价要小于原价，已参与其他促销，未找到sku，商品未上架，活动库存应小于可售库存，活动库存应是正整数
func validateSeckillData(row []string, skus map[int]*models.UpetGoods, count int, currentGDate *nMutualTime) (r *RowSeckillResult) {
	r = &RowSeckillResult{}
	skuId := cast.ToInt(row[0])
	if skuId < 1 {
		r.FailReason = "Sku无效"
		return
	}
	//判断库存是否为整数

	// 多次的算失败
	if count > 1 {
		r.FailReason = "存在重复导入"
		return
	}

	if sku, has := skus[skuId]; !has {
		r.FailReason = "Sku不存在或已下架"
		return
	} else {
		r.Sku = sku
	}
	if rate, err := cast.ToFloat32E(row[1]); err != nil {
		r.FailReason = "秒杀价格式错误"
		return
	} else {
		// 佣金等于0表示取消
		r.GoodsPrice = cast.ToFloat32(r.Sku.GoodsPrice)
		if ((rate-0.01) < -0.001 && rate != 0) || (rate-r.GoodsPrice) > 0.001 || rate == r.GoodsPrice {
			r.FailReason = "秒杀价要小于原价且不能为0"
			return
		}
	}
	if r.Sku.IsVirtual == 1 {
		r.FailReason = "虚拟商品不参与活动"
		return
	}

	promotionTypeMap := map[int32]string{
		2:  "限时折扣",
		3:  "秒杀",
		4:  "闪购",
		5:  "拼团",
		6:  "周期购",
		7:  "新人专享",
		8:  "预售",
		9:  "新秒杀",
		10: "医保活动",
	}
	//获取要添加商品活动开始结束时间
	gDates := make([]nMutualTime, 0)
	promotionTypes := make([]int32, 0)
	promotionTypes = []int32{models.UpetPTimeDiscountLimit, models.UpetPTimeOldSeckill, models.UpetPTimeLightning,
		models.UpetPTimePinTuan, models.UpetPTimeCycleBuy, models.UpetPTimePresell, models.UpetPTimeNewPerson, models.UpetInsurance}
	//查询电商活动
	models.GetDBConn().Table("upet_p_time").
		Select("start_time AS begin_date,end_time AS end_date,promotion_type").
		Where("goods_id = ? and end_time >? and store_id =1", skuId, time.Now().Unix()).In("promotion_type", promotionTypes).Find(&gDates)

	//判断时间是否重叠
	if len(gDates) > 0 {
		for _, gDate := range gDates {
			if !(currentGDate.EndDate.Before(gDate.BeginDate) || currentGDate.BeginDate.After(gDate.EndDate)) { //重叠
				_, ok := promotionTypeMap[gDate.PromotionType]
				if ok {
					r.FailReason = "已参与其它活动促销"
					return
				}
			}
		}
	}

	//判断是否与助力活动重叠
	zDates := make([]nMutualTime, 0)
	models.GetDcDBConn().Table("market_activity_product").Alias("map").
		Select("ma_start_date AS begin_date,ma_end_date AS end_date").
		Join("inner", "market_activity as ma", "ma.id = map.ma_activity_id").
		Where("map.ma_product_sku = ? AND ma.ma_end_date >= ? AND ma.ma_status =1", skuId, time.Now().Unix()).
		Find(&zDates)
	if len(zDates) > 0 {
		for _, zDate := range zDates {
			if !(currentGDate.EndDate.Before(zDate.BeginDate) || currentGDate.BeginDate.After(zDate.EndDate)) { //重叠
				r.FailReason = "已参与其它助力促销"
				return
			}
		}
	}
	//判断赠品有效时间
	if r.Sku.HaveGift == 1 {
		giftinfo := make([]giftField, 0)
		models.GetDBConn().Table("upet_goods_gift").
			Select("start_time,end_time").
			Where("goods_id = ? AND end_time >?", skuId, time.Now().Format(kit.DATETIME_LAYOUT)).Find(&giftinfo)
		if len(giftinfo) > 0 {
			r.FailReason = "已参与其它商品赠品促销"
			return
		}
	}
	r.State = 1
	return
}
