package distribution

import (
	"_/models"
	"_/proto/sh"
	"errors"
	"fmt"
	"github.com/go-xorm/xorm"
)

// SkuUpdateStateByReq 更新分销状态
func SkuUpdateStateByReq(session *xorm.Session, sku *models.UpetGoods, in *sh.DisSkuUpdateRequest) (msg string, err error) {
	if in.IsDis.Value {
		// 已经是分销商品
		if sku.IsDis > 0 {
			return "", errors.New("分销商品不允许重新添加")
		}
		rate := float32(1)
		// 不存在设置时查找默认佣金
		if in.NormalCommissionRate != nil && in.NormalCommissionRate.Value != 0 {
			rate = in.NormalCommissionRate.Value
			if (rate-0.01) < -0.001 || (rate-50) > 0.001 {
				return "", errors.New("佣金比例最小为0.01，最大为50")
			}
		} else {
			if has, err := session.Table("upet_store_extend").Where("store_id = ? and dis_commis_rate > 0", in.OrgId).Select("dis_commis_rate").Get(&rate); err != nil {
				return "", errors.New("查询默认佣金出错 " + err.Error())
			} else if !has {
				return "", errors.New("不存在默认佣金设置")
			}
		}
		if err = sku.AddDis(session, rate, "", in.OrgId); err == nil {
			msg = fmt.Sprintf("添加分销商品%v 日常佣金%v%%", in.SkuId, rate)
		}
		return
	}
	// 已经是分销商品
	if sku.IsDis <= 0 {
		return "", errors.New("非分销商品不允许取消")
	}
	if err = sku.CancelDis(session, in.OrgId); err == nil {
		msg = fmt.Sprintf("取消分销商品%v", in.SkuId)
	}
	return
}
