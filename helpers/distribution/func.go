package distribution

import (
	"_/models"
	"context"
	"google.golang.org/grpc/metadata"
	"time"
)

// InsertLog 插入操作日志
func InsertLog(ctx context.Context, log *models.UpetDisGoodsLog) error {
	db := models.GetDBConn()

	if md, success := metadata.FromIncomingContext(ctx); success {
		if len(md.Get("user-name")) > 0 {
			log.UserName = md.Get("user-name")[0]
		}
	}
	if log.CreatedAt.IsZero() {
		log.CreatedAt = time.Now().Local()
	}
	_, err := db.Insert(log)
	return err
}
