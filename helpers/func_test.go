package helpers

import (
	"github.com/xuri/excelize/v2"
	"testing"
)

func TestGetGoodsListUrl(t *testing.T) {
	t.<PERSON><PERSON>(GetGoodsListUrl("1234234"))
}

func TestUploadExcelToQi<PERSON>iu(t *testing.T) {
	f := excelize.NewFile()
	_ = f.<PERSON>("Sheet1", "A1", "SKU")
	_ = f.Set<PERSON>ell<PERSON>ue("Sheet1", "B1", "佣金（百分比数字部分，必填，如填0表示取消分销）")
	_ = f.<PERSON>ell<PERSON>alue("Sheet1", "C1", "推广文案（选填）")

	type args struct {
		file *excelize.File
		name string
	}
	tests := []struct {
		name    string
		args    args
		wantUrl string
		wantErr bool
	}{
		{
			args: args{
				file: f,
				name: "",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotUrl, err := UploadExcelToQiNiu(tt.args.file, tt.args.name)

			t.Log(gotUrl)
			if (err != nil) != tt.wantErr {
				t.<PERSON>("UploadExcelToQiNiu() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
