package main

import (
	"_/proto/mm"
	"_/proto/sh"
	"_/services"
	"_/tasks"
	"flag"
	"net/http"
	"os"
	"strings"
	"time"

	_ "net/http/pprof"

	"github.com/ppkg/kit"

	"github.com/limitedlee/microservice/micro"
	"github.com/maybgit/glog"
	"google.golang.org/grpc/reflection"
)

func init() {
	sh, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = sh

	env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	if !(env == "pro" && env == "production") {
		kit.IsDebug = true
	}
}

func main() {
	go func() {
		err := http.ListenAndServe(":11125", nil)
		if err != nil {
			return
		}
	}() //pprof性能分析

	defer glog.Flush()
	flag.Parse()
	micro := micro.MicService{}
	micro.NewServer()

	//初始化定时任务
	tasks.InitTask()

	//服务注册
	sh.RegisterTestServiceServer(micro.GrpcServer, services.TestService{})
	sh.RegisterProductServiceServer(micro.GrpcServer, services.Product{})
	mm.RegisterMemberMergeServiceServer(micro.GrpcServer, services.Member{})
	sh.RegisterDistributionServiceServer(micro.GrpcServer, &services.Distribution{})
	sh.RegisterVoucherServiceServer(micro.GrpcServer, &services.Voucher{})
	sh.RegisterActivityServiceServer(micro.GrpcServer, &services.ActivityService{})

	//服务反射，便于查看grpc的状态
	reflection.Register(micro.GrpcServer)
	glog.Info("upet-center服务启动...")

	//addressIp:=utils.GetClientIp()

	//println(addressIp)

	micro.Start()
}
