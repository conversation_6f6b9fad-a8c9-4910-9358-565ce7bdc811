package models

type UpetDisGoodsImportResult struct {
	Id               int     `xorm:"not null pk autoincr INT(10)"`
	ImportId         int     `xorm:"not null comment('关联导入id') index INT(11)"`
	Sku              int     `xorm:"not null comment('商品id') INT(11)"`
	BeforeCommisRate float32 `xorm:"comment('导入前佣金比例') DECIMAL(4,2)"`
	CommisRate       float32 `xorm:"comment('佣金比例') DECIMAL(4,2)"`
	BeforeWrite      string  `xorm:"comment('导入前分销文案') VARCHAR(255)"`
	Write            string  `xorm:"comment('分销文案') VARCHAR(255)"`
	State            int     `xorm:"not null default 1 comment('导入状态，0失败，1成功') TINYINT(4)"`
	FailReason       string  `xorm:"not null default '' comment('失败原因') VARCHAR(255)"`
}
