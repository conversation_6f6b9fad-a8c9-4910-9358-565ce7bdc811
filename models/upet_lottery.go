package models

import "time"

// 锦鲤活动列表
type UpetLotteryDraw struct {
	Id               int    `json:"id" xorm:"pk autoincr not null INT(10) 'id'"`
	PeriodNumber     string `json:"period_number" xorm:"default 0 comment('当前期数') VARCHAR(10) 'period_number'"`
	LotteryTitle     string `json:"lottery_title" xorm:"default '' comment('活动标题') VARCHAR(50) 'lottery_title'"`
	LotteryStartTime int64  `json:"lottery_start_time" xorm:"default 0 comment('开始时间') INT(11) 'lottery_start_time'"`
	LotteryEndTime   int64  `json:"lottery_end_time" xorm:"default 0 comment('结束时间') INT(11) 'lottery_end_time'"`
	ShareImageUrl    string `json:"share_image_url" xorm:"default 'null' VARCHAR(50) 'share_image_url'"`
	DrawImageUrl     string `json:"draw_image_url" xorm:"default '' comment('抽奖图片') VARCHAR(50) 'draw_image_url'"`
	LiveImageUrl     string `json:"live_image_url" xorm:"default '' comment('直播图片') VARCHAR(50) 'live_image_url'"`
	LiveMiniPath     string `json:"live_mini_path" xorm:"default '' comment('直播跳转路径') VARCHAR(200) 'live_mini_path'"`
	AdvContent       string `json:"adv_content" xorm:"default 'null' comment('广告轮播多图') TEXT 'adv_content'"`
	AdvImageUrl      string `json:"adv_image_url" xorm:"default '' comment('广告图片') VARCHAR(50) 'adv_image_url'"`
	AdvPath          string `json:"adv_path" xorm:"default '' comment('广告跳转路径') VARCHAR(100) 'adv_path'"`
	AdvTitle         string `json:"adv_title" xorm:"default '' comment('广告名称') VARCHAR(30) 'adv_title'"`
	LotteryState     int32  `json:"lottery_state" xorm:"default 0 comment('0,未开始，1已开始，2已结束') TINYINT(4) 'lottery_state'"`
	DelState         int32  `json:"del_state" xorm:"default 1 comment('0已删除，1正常') TINYINT(4) 'del_state'"`
	AddTime          int    `json:"add_time" xorm:"default 0 comment('添加时间') INT(11) 'add_time'"`
	MemberId         int    `json:"member_id" xorm:"default 0 comment('中奖用户id') INT(11) 'member_id'"`
	LotteryNumber    string `json:"lottery_number" xorm:"default 0 comment('开奖号') VARCHAR(10) 'lottery_number'"`
	AdminId          int    `json:"admin_id" xorm:"default 0 comment('操作人id') INT(11) 'admin_id'"`
	LoginImageUrl    string `json:"login_image_url" xorm:"default '' comment('登录图片') VARCHAR(50) 'login_image_url'"`
	SImageUrl        string `json:"s_image_url" xorm:"default '' comment('分享海报图片') VARCHAR(50) 's_image_url'"`
}

// 幸运码
type UpetLotteryNumbers struct {
	Id               int    `json:"id" xorm:"pk autoincr not null INT(10) 'id'"`
	MemberId         int64  `json:"member_id" xorm:"default 0 comment('用户id') INT(11) 'member_id'"`
	MemberName       string `json:"member_name" xorm:"default '' comment('用户名') VARCHAR(30) 'member_name'"`
	InviteMemberId   int64  `json:"invite_member_id" xorm:"default 0 comment('被邀请人用户id') INT(11) 'invite_member_id'"`
	InviteMemberName string `json:"invite_member_name" xorm:"default '' comment('被邀请人用户名') VARCHAR(30) 'invite_member_name'"`
	LotteryNumber    string `json:"lottery_number" xorm:"default 0 comment('生成幸运码') VARCHAR(10) 'lottery_number'"`
	LotteryId        int64  `json:"lottery_id" xorm:"default 0 comment('对应活动id') INT(11) 'lottery_id'"`
	State            int32  `json:"state" xorm:"default 1 comment('1系统赠送，2分享邀请获得，3、邀请好友、4、下单消费') TINYINT(4) 'state'"`
	AddTime          int64  `json:"add_time" xorm:"default 0 comment('添加时间') INT(11) 'add_time'"`
	IsLottery        int32  `json:"is_lottery" xorm:"default 0 comment('默认0未中奖，1中奖') TINYINT(4) 'is_lottery'"`
	OrderSn          string `json:"order_sn" xorm:"default '' comment('主单号') VARCHAR(50) 'order_sn'"`
}

// state装换成名称
func GetLotteryNumbersStateName(state int32) (str string) {
	switch state {
	case 1:
		str = "首次参加活动"
	case 2:
		str = "分享活动"
	case 3:
		str = "邀请好友"
	case 4:
		str = "消费"
	}
	return
}

// 920锦鲤中奖信息
type UpetLotteryReward struct {
	Id            int       `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	MemberId      int64     `json:"member_id" xorm:"not null comment('用户id') INT(11) 'member_id'"`
	LotteryId     int       `json:"lottery_id" xorm:"not null default 0 comment('活动id') INT(11) 'lottery_id'"`
	LotteryNumber string    `json:"lottery_number" xorm:"not null default '' comment('幸运码') VARCHAR(10) 'lottery_number'"`
	OpenTimeId    int       `json:"open_time_id" xorm:"not null default 0 comment('upet_lottery_open_time表的id，开奖日期') INT(10) 'open_time_id'"`
	RewardId      int32     `json:"reward_id" xorm:"not null default 0 comment('奖品，1-盲盒，2-免单') TINYINT(4) 'reward_id'"`
	Mobile        string    `json:"mobile" xorm:"not null comment('中奖手机号') CHAR(11) 'mobile'"`
	Avatar        string    `json:"avatar" xorm:"default '' comment('头像') VARCHAR(255) 'avatar'"`
	AddTime       time.Time `json:"add_time" xorm:"not null DATETIME 'add_time'"`
}

// 开奖日期配置表
type UpetLotteryOpenTime struct {
	Id              int       `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	OpenTime        time.Time `json:"open_time" xorm:"not null comment('开奖时间') DATETIME 'open_time'"`
	Status          int32     `json:"status" xorm:"not null default 0 comment('默认0-未开奖，1-已开奖，2-开奖失败') TINYINT(2) 'status'"`
	Type            int32     `json:"type" xorm:"not null default 1 comment('默认1-小奖，2-大奖') TINYINT(2) 'type'"`
	RewardFirstNum  int       `json:"reward_first_num" xorm:"not null default 0 comment('奖品1的数量，免单') INT(11) 'reward_first_num'"`
	RewardSecondNum int       `json:"reward_second_num" xorm:"default 0 comment('奖品2数量，礼品') INT(11) 'reward_second_num'"`
	CreatedAt       time.Time `json:"add_time" xorm:"not null DATETIME 'created_at'"`
	UpdatedAt       time.Time `json:"update_time xorm:"null DATETIME 'updated_at'"`
}

// 中奖用户收货地址
type UpetLotteryRewardAddress struct {
	Id         int       `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	LotteryId  int32     `json:"lottery_id" xorm:"not null default 0 comment('活动id') INT(11) 'lottery_id'"`
	Name       string    `json:"name" xorm:"not null default '' comment('收件人') VARCHAR(10) 'name'"`
	City       string    `json:"city" xorm:"default 0 comment('城市') VARCHAR(10) 'city'"`
	Address    string    `json:"address" xorm:"not null default '' comment('详细地址') VARCHAR(255) 'address'"`
	ScrmUserId string    `json:"scrm_user_id" xorm:"not null default '' comment('用户scrm_user_id') VARCHAR(32) 'scrm_user_id'"`
	Mobile     string    `json:"mobile" xorm:"not null default '' comment('手机号') VARCHAR(11) 'mobile'"`
	Adcode     string    `json:"adcode" xorm:"default '' comment('城市编码') VARCHAR(10) 'adcode'"`
	TxLat      float64   `json:"tx_lat" xorm:"default '0.0000000000000' comment('腾讯地图纬度') DECIMAL(17) 'tx_lat'"`
	TxLng      float64   `json:"tx_lng" xorm:"default '0.0000000000000' comment('腾讯地图经纬度') DECIMAL(17) 'tx_lng'"`
	HouseInfo  string    `json:"house_info" xorm:"default '' comment('门牌号') VARCHAR(255) 'house_info'"`
	AddTime    time.Time `json:"add_time" xorm:"not null comment('添加时间') DATETIME 'add_time'"`
	UpdateTime time.Time `json:"update_time" xorm:"default 'null' comment('更新时间') DATETIME 'update_time'"`
}

// 邀请记录
type UpetLotteryMembers struct {
	Id             int   `json:"id" xorm:"pk autoincr not null INT(10) 'id'"`
	LotteryId      int32 `json:"lottery_id" xorm:"default 0 comment('活动id') INT(11) 'lottery_id'"`
	MemberId       int64 `json:"member_id" xorm:"default 0 comment('当前用户id') INT(11) 'member_id'"`
	MemberInviteId int64 `json:"member_invite_id" xorm:"default 0 comment('发起分享用户id') INT(11) 'member_invite_id'"`
	AddTime        int64 `json:"add_time" xorm:"default 0 comment('添加时间') INT(11) 'add_time'"`
}
