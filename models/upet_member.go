package models

import (
	"time"

	"github.com/maybgit/glog"
)

type UpetMember struct {
	MemberId              int64     `json:"member_id" xorm:"pk autoincr not null comment('会员id') INT 'member_id'"`
	ScrmUserId            string    `json:"scrm_user_id" xorm:"not null comment('SCRM的用户user_id') VARCHAR(32) 'scrm_user_id'"`
	MemberName            string    `json:"member_name" xorm:"default 'null' comment('会员名称') VARCHAR(50) 'member_name'"`
	MemberTruename        string    `json:"member_truename" xorm:"default 'null' comment('真实姓名') VARCHAR(20) 'member_truename'"`
	MemberAvatar          string    `json:"member_avatar" xorm:"default 'null' comment('会员头像') VARCHAR(255) 'member_avatar'"`
	MemberWxavatar        string    `json:"member_wxavatar" xorm:"default 'null' comment('微信头像') VARCHAR(255) 'member_wxavatar'"`
	MemberSex             int64     `json:"member_sex" xorm:"default 'null' comment('会员性别') TINYINT(1) 'member_sex'"`
	MemberBirthday        time.Time `json:"member_birthday" xorm:"default 'null' comment('生日') DATE 'member_birthday'"`
	MemberPasswd          string    `json:"member_passwd" xorm:"default 'null' comment('会员密码') VARCHAR(32) 'member_passwd'"`
	MemberPaypwd          string    `json:"member_paypwd" xorm:"default 'null' comment('支付密码') CHAR(32) 'member_paypwd'"`
	MemberEmail           string    `json:"member_email" xorm:"default '''' comment('会员邮箱') VARCHAR(100) 'member_email'"`
	MemberEmailBind       int64     `json:"member_email_bind" xorm:"not null default 0 comment('0未绑定1已绑定') TINYINT 'member_email_bind'"`
	MemberMobile          string    `json:"member_mobile" xorm:"default 'null' comment('手机号') VARCHAR(11) 'member_mobile'"`
	MemberMobileBind      int64     `json:"member_mobile_bind" xorm:"not null default 0 comment('0未绑定1已绑定') TINYINT 'member_mobile_bind'"`
	MemberQq              string    `json:"member_qq" xorm:"default 'null' comment('qq') VARCHAR(100) 'member_qq'"`
	MemberWw              string    `json:"member_ww" xorm:"default 'null' comment('阿里旺旺') VARCHAR(100) 'member_ww'"`
	MemberLoginNum        int64     `json:"member_login_num" xorm:"not null default 1 comment('登录次数') INT 'member_login_num'"`
	MemberTime            string    `json:"member_time" xorm:"not null comment('会员注册时间') VARCHAR(10) 'member_time'"`
	MemberLoginTime       string    `json:"member_login_time" xorm:"not null comment('当前登录时间') VARCHAR(10) 'member_login_time'"`
	MemberOldLoginTime    string    `json:"member_old_login_time" xorm:"not null comment('上次登录时间') VARCHAR(10) 'member_old_login_time'"`
	MemberLoginIp         string    `json:"member_login_ip" xorm:"default 'null' comment('当前登录ip') VARCHAR(20) 'member_login_ip'"`
	MemberOldLoginIp      string    `json:"member_old_login_ip" xorm:"default 'null' comment('上次登录ip') VARCHAR(20) 'member_old_login_ip'"`
	MemberQqopenid        string    `json:"member_qqopenid" xorm:"default 'null' comment('qq互联id') VARCHAR(100) 'member_qqopenid'"`
	MemberQqinfo          string    `json:"member_qqinfo" xorm:"comment('qq账号相关信息') TEXT 'member_qqinfo'"`
	MemberSinaopenid      string    `json:"member_sinaopenid" xorm:"default 'null' comment('新浪微博登录id') VARCHAR(100) 'member_sinaopenid'"`
	MemberSinainfo        string    `json:"member_sinainfo" xorm:"comment('新浪账号相关信息序列化值') TEXT 'member_sinainfo'"`
	WeixinUnionid         string    `json:"weixin_unionid" xorm:"default 'null' comment('微信用户统一标识') VARCHAR(50) 'weixin_unionid'"`
	WeixinInfo            string    `json:"weixin_info" xorm:"default 'null' comment('微信用户相关信息') VARCHAR(255) 'weixin_info'"`
	MemberPoints          int64     `json:"member_points" xorm:"not null default 0 comment('会员积分') INT 'member_points'"`
	AvailablePredeposit   string    `json:"available_predeposit" xorm:"not null default '0.00' comment('预存款可用金额') DECIMAL(10) 'available_predeposit'"`
	FreezePredeposit      string    `json:"freeze_predeposit" xorm:"not null default '0.00' comment('预存款冻结金额') DECIMAL(10) 'freeze_predeposit'"`
	AvailableRcBalance    string    `json:"available_rc_balance" xorm:"not null default '0.00' comment('可用充值卡余额') DECIMAL(10) 'available_rc_balance'"`
	FreezeRcBalance       string    `json:"freeze_rc_balance" xorm:"not null default '0.00' comment('冻结充值卡余额') DECIMAL(10) 'freeze_rc_balance'"`
	InformAllow           int64     `json:"inform_allow" xorm:"not null default 1 comment('是否允许举报(1可以/2不可以)') TINYINT(1) 'inform_allow'"`
	IsBuy                 int64     `json:"is_buy" xorm:"not null default 1 comment('会员是否有购买权限 1为开启 0为关闭') TINYINT(1) 'is_buy'"`
	IsAllowtalk           int64     `json:"is_allowtalk" xorm:"not null default 1 comment('会员是否有咨询和发送站内信的权限 1为开启 0为关闭') TINYINT(1) 'is_allowtalk'"`
	MemberState           int64     `json:"member_state" xorm:"not null default 1 comment('会员的开启状态 1为开启 0为关闭') TINYINT(1) 'member_state'"`
	MemberSnsvisitnum     int64     `json:"member_snsvisitnum" xorm:"not null default 0 comment('sns空间访问次数') INT 'member_snsvisitnum'"`
	MemberAreaid          int64     `json:"member_areaid" xorm:"default 'null' comment('地区ID') INT 'member_areaid'"`
	MemberCityid          int64     `json:"member_cityid" xorm:"default 'null' comment('城市ID') INT 'member_cityid'"`
	MemberProvinceid      int64     `json:"member_provinceid" xorm:"default 'null' comment('省份ID') INT 'member_provinceid'"`
	MemberAreainfo        string    `json:"member_areainfo" xorm:"default 'null' comment('地区内容') VARCHAR(255) 'member_areainfo'"`
	MemberPrivacy         string    `json:"member_privacy" xorm:"comment('隐私设定') TEXT 'member_privacy'"`
	MemberExppoints       int64     `json:"member_exppoints" xorm:"not null default 0 comment('会员经验值') INT 'member_exppoints'"`
	TradAmount            string    `json:"trad_amount" xorm:"default '0.00' comment('可提现金额') DECIMAL(12) 'trad_amount'"`
	AuthMessage           string    `json:"auth_message" xorm:"default 'null' comment('审核意见') VARCHAR(255) 'auth_message'"`
	DistriState           int64     `json:"distri_state" xorm:"default 0 comment('分销状态 0未申请 1待审核 2已通过 3未通过 4清退 5退出') TINYINT(1) 'distri_state'"`
	BillUserName          string    `json:"bill_user_name" xorm:"default 'null' comment('收款人姓名') VARCHAR(255) 'bill_user_name'"`
	BillTypeCode          string    `json:"bill_type_code" xorm:"default 'null' comment('结算账户类型') VARCHAR(255) 'bill_type_code'"`
	BillTypeNumber        string    `json:"bill_type_number" xorm:"default 'null' comment('收款账号') VARCHAR(255) 'bill_type_number'"`
	BillBankName          string    `json:"bill_bank_name" xorm:"default 'null' comment('开户行') VARCHAR(255) 'bill_bank_name'"`
	FreezeTrad            string    `json:"freeze_trad" xorm:"default '0.00' comment('冻结佣金') DECIMAL(12) 'freeze_trad'"`
	DistriCode            string    `json:"distri_code" xorm:"default 'null' comment('分销代码') VARCHAR(255) 'distri_code'"`
	DistriFormId          string    `json:"distri_formId" xorm:"default 'null' comment('小程序formId') VARCHAR(30) 'distri_formId'"`
	DistriChainid         int64     `json:"distri_chainid" xorm:"default 0 comment('分销门店ID') INT 'distri_chainid'"`
	DistriBrandid         int64     `json:"distri_brandid" xorm:"default 0 comment('品牌ID') INT 'distri_brandid'"`
	DistriTime            int64     `json:"distri_time" xorm:"default 'null' comment('申请时间') INT 'distri_time'"`
	DistriHandleTime      int64     `json:"distri_handle_time" xorm:"default 'null' comment('处理时间') INT 'distri_handle_time'"`
	DistriShow            int64     `json:"distri_show" xorm:"not null default 0 comment('分销中心是否显示 0不显示 1显示') TINYINT(1) 'distri_show'"`
	QuitTime              int64     `json:"quit_time" xorm:"default 'null' comment('退出时间') INT 'quit_time'"`
	DistriApplyTimes      int64     `json:"distri_apply_times" xorm:"default 0 comment('申请次数') INT 'distri_apply_times'"`
	DistriQuitTimes       int64     `json:"distri_quit_times" xorm:"default 0 comment('退出次数') INT 'distri_quit_times'"`
	WeixinMpOpenid        string    `json:"weixin_mp_openid" xorm:"default 'null' comment('微信公众号OpenID') VARCHAR(50) 'weixin_mp_openid'"`
	IsCash                int64     `json:"is_cash" xorm:"default 1 comment('是否允许提现，0否，1是') TINYINT(1) 'is_cash'"`
	IdCardName            string    `json:"id_card_name" xorm:"default '' comment('实名认证姓名') VARCHAR(20) 'id_card_name'"`
	IdCardCode            string    `json:"id_card_code" xorm:"default '' comment('身份证号') VARCHAR(20) 'id_card_code'"`
	IdCardBind            int64     `json:"id_card_bind" xorm:"not null default 0 comment('是否实名认证0否,1是') TINYINT(1) 'id_card_bind'"`
	IdCardState           int64     `json:"id_card_state" xorm:"not null default 0 comment('审核状态0未申请，1待审核，2审核成功，3审核失败') TINYINT(1) 'id_card_state'"`
	IdCardExplain         string    `json:"id_card_explain" xorm:"default '' comment('审核说明') VARCHAR(50) 'id_card_explain'"`
	IdCardImg             string    `json:"id_card_img" xorm:"default '' comment('身份证正反面图片') VARCHAR(50) 'id_card_img'"`
	WeixinMiniOpenid      string    `json:"weixin_mini_openid" xorm:"default '' comment('小程序openid(阿闻宠物-北京那边用)') VARCHAR(50) 'weixin_mini_openid'"`
	WeixinMiniAddtime     int64     `json:"weixin_mini_addtime" xorm:"default 0 comment('小程序绑定时间(阿闻宠物-北京那边用)') INT 'weixin_mini_addtime'"`
	WeixinMiniOpenidshop  string    `json:"weixin_mini_openidshop" xorm:"default '' comment('小程序openid(阿闻智慧门店-自用)') VARCHAR(50) 'weixin_mini_openidshop'"`
	WeixinMiniAddtimeshop int64     `json:"weixin_mini_addtimeshop" xorm:"default 0 comment('小程序绑定时间(阿闻智慧门店-自用)') INT 'weixin_mini_addtimeshop'"`
	WeixinMiniOpenidasq   string    `json:"weixin_mini_openidasq" xorm:"default '' comment('小程序openid(阿闻爱省钱-自用)') VARCHAR(50) 'weixin_mini_openidasq'"`
	WeixinMiniAddtimeasq  int64     `json:"weixin_mini_addtimeasq" xorm:"default 0 comment('小程序绑定时间(阿闻爱省钱-自用)') INT 'weixin_mini_addtimeasq'"`
	WeixinMiniOpenidmall  string    `json:"weixin_mini_openidmall" xorm:"default '' comment('小程序openid(阿闻商城-自用)') VARCHAR(50) 'weixin_mini_openidmall'"`
	WeixinMiniAddtimemall int64     `json:"weixin_mini_addtimemall" xorm:"default 0 comment('小程序绑定时间(阿闻商城-自用)') INT 'weixin_mini_addtimemall'"`
	EarnestMoney          string    `json:"earnest_money" xorm:"default '0.00' comment('保证金金额') DECIMAL(12) 'earnest_money'"`
	GevalCommentStatus    int64     `json:"geval_comment_status" xorm:"default 0 comment('0为电商 1为采集 2为宠医云 3阿闻智慧医院 4阿闻小程序 5阿闻爱省钱 6阿闻商城 7数据中心 8佳雯会员') TINYINT 'geval_comment_status'"`
	DisTradMoney          string    `json:"dis_trad_money" xorm:"default '0.00' comment('累计收益') DECIMAL(12) 'dis_trad_money'"`
	BillBankBranch        string    `json:"bill_bank_branch" xorm:"default 'null' comment('开户银行支行名称') VARCHAR(30) 'bill_bank_branch'"`
	MemberIsvip           int64     `json:"member_isvip" xorm:"default 0 comment('0.默认1.198会员') TINYINT(1) 'member_isvip'"`
	MemberIsbzk           int64     `json:"member_isbzk" xorm:"default 0 comment('0.默认1.保障卡') TINYINT(1) 'member_isbzk'"`
	MemberVipstime        int64     `json:"member_vipstime" xorm:"default 0 comment('会员开始时间') INT 'member_vipstime'"`
	MemberVipetime        int64     `json:"member_vipetime" xorm:"default 0 comment('会员过期时间') INT 'member_vipetime'"`
	MemberBzkstime        int64     `json:"member_bzkstime" xorm:"default 0 comment('保障卡开始时间') INT 'member_bzkstime'"`
	MemberBzketime        int64     `json:"member_bzketime" xorm:"default 0 comment('保障卡结束时间') INT 'member_bzketime'"`
	UserLevelId           int64     `json:"user_level_id" xorm:"default 0 comment('会员等级，来源dc_customer.user_level表') INT 'user_level_id'"`
	UserLevelStime        int64     `json:"user_level_stime" xorm:"default 0 comment('会员等级开始时间') INT 'user_level_stime'"`
	UserLevelEtime        int64     `json:"user_level_etime" xorm:"default 0 comment('会员等级过期时间') INT 'user_level_etime'"`
	MemberIdentity        string    `json:"member_identity" xorm:"default 'null' comment('分销员身份证') VARCHAR(50) 'member_identity'"`
	MemberMobileBefore    string    `json:"member_mobile_before" xorm:"default 'null' comment('修改前手机号') VARCHAR(11) 'member_mobile_before'"`
	MemberVipstarttime    int64     `json:"member_vipstarttime" xorm:"default 0 comment('电商198会员开始时间') INT 'member_vipstarttime'"`
	MemberVipendtime      int64     `json:"member_vipendtime" xorm:"default 0 comment('电商198会员结束时间') INT 'member_vipendtime'"`
	NewcomerTag           int64     `json:"newcomer_tag" xorm:"default 0 comment('新用户标记，null未更新、1=新人、2，有可能成为新人、3=老用户') TINYINT 'newcomer_tag'"`
	WeixinMiniOpenid2     string    `json:"weixin_mini_openid2" xorm:"default '' comment('极宠家微信小程序openid') VARCHAR(255) 'weixin_mini_openid2'"`
}

func (m *UpetMember) FindMemberIdByUserIds(scrmUserIds []string) (map[string]int64, error) {
	data := make(map[string]int64, 0)
	list := make([]UpetMember, 0)
	err := GetDBConn().Table("upet_member").Cols("member_id,scrm_user_id").In("scrm_user_id", scrmUserIds).Find(&list)
	if err != nil {
		glog.Error("UpetMember.FindMemberIdByUserIds error: ", err)
		return data, err
	}
	for _, v := range list {
		data[v.ScrmUserId] = v.MemberId
	}
	return data, nil
}

type MemberMobile struct {
	MemberId     int64  `json:"member_id" xorm:"pk autoincr not null comment('会员id') INT 'member_id'"`
	MemberMobile string `json:"member_mobile" xorm:"default 'null' comment('手机号') VARCHAR(11) 'member_mobile'"`
}

func (m *UpetMember) FindMobileByMemberIds(memberIds []int64) (map[int64]string, error) {
	data := make(map[int64]string, 0)
	list := make([]MemberMobile, 0)
	err := GetDBConn().Table("upet_member").Cols("member_id,member_mobile").In("member_id", memberIds).Find(&list)
	if err != nil {
		glog.Error("UpetMember.FindMobileByMemberIds error: ", err)
		return data, err
	}
	for _, v := range list {
		data[v.MemberId] = v.MemberMobile
	}
	return data, nil
}

func (m *UpetMember) FindByScrmUserId(scrmUserId string) (UpetMember, error) {
	var detail UpetMember
	_, err := GetDBConn().Table("upet_member").Cols("member_id,scrm_user_id,user_level_id,user_level_stime,user_level_etime").
		Where("scrm_user_id=?", scrmUserId).Get(&detail)
	if err != nil {
		glog.Error("UpetMember.FindByScrmUserId error: ", err)
	}
	return detail, err
}
