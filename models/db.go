package models

import (
	"os"

	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	kit "github.com/tricobbler/rp-kit"
)

var (
	xormEngine       *kit.DBEngine
	dcEngine         *kit.DBEngine
	DcActivieyEngine *kit.DBEngine
)

// GetDBConn
// 建立阿闻数据库连接池
func GetDBConn() *xorm.Engine {
	if xormEngine == nil || xormEngine.Engine == nil {
		dsn := config.GetString("mysql.upetmart")
		//dsn = "root:d&!89iCEGKOuVHkT@(123.57.167.33:23306)/testupetmart?charset=utf8mb4" //sit1
		//dsn = "root:XjIrQepuHn7u^E8D@(39.106.30.60:13306)/testupetmart?charset=utf8mb4" //uat1
		//dsn = "root:d&!89iCEGKOuVHkT@(39.107.46.194:13306)/testupetmart?charset=utf8mb4" //sit2
		xormEngine = kit.NewDBEngine(dsn)
	}
	engine := xormEngine.Engine.(*xorm.Engine)
	// 开发环境显示sql日志
	if os.Getenv("ASPNETCORE_ENVIRONMENT") == "" {
		engine.ShowSQL(true)
	}
	return engine
}

// GetDcDBConn
// 建立阿闻数据库连接池
func GetDcDBConn() *xorm.Engine {
	if dcEngine == nil || dcEngine.Engine == nil {
		dcEngine = kit.NewDBEngine(config.GetString("mysql.datacenter"))
	}
	engine := dcEngine.Engine.(*xorm.Engine)
	// 开发环境显示sql日志
	if os.Getenv("ASPNETCORE_ENVIRONMENT") == "" {
		engine.ShowSQL(true)
	}
	return engine
}

func GetActivityDBConn() *xorm.Engine {
	if DcActivieyEngine == nil || DcActivieyEngine.Engine == nil {
		dsn := config.GetString("mysql.dc_activity")
		//dsn = "root:d&!89iCEGKOuVHkT@(123.57.167.33:23306)/dc_activity?charset=utf8mb4" //sit1
		//dsn = "root:XjIrQepuHn7u^E8D@(39.106.30.60:13306)/dc_activity?charset=utf8mb4" //uat1
		DcActivieyEngine = kit.NewDBEngine(dsn)
	}
	engine := DcActivieyEngine.Engine.(*xorm.Engine)
	// 开发环境显示sql日志
	if os.Getenv("ASPNETCORE_ENVIRONMENT") == "" {
		engine.ShowSQL(true)
	}
	return engine
}
