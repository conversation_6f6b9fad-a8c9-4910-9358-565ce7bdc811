package models

// 电商 upet_p_time表的活动类型
const (
	_ = iota
	_
	//限时折扣-2
	UpetPTimeDiscountLimit
	//秒杀-3
	UpetPTimeOldSeckill
	//闪购-4
	UpetPTimeLightning
	//拼团-5
	UpetPTimePinTuan
	//周期购-6
	UpetPTimeCycleBuy
	//新人专享-7
	UpetPTimeNewPerson
	//预售-8
	UpetPTimePresell
	// 新秒杀-9
	UpetPTimeSeckill
	// 医保活动
	UpetInsurance
)

var UpetPTimeMap map[int32]string = map[int32]string{
	UpetPTimeDiscountLimit: "限时折扣",
	UpetPTimeOldSeckill:    "秒杀",
	UpetPTimeLightning:     "闪购",
	UpetPTimePinTuan:       "拼团",
	UpetPTimeCycleBuy:      "周期购",
	UpetPTimeNewPerson:     "新人专享",
	UpetPTimePresell:       "预售",
	UpetPTimeSeckill:       "新秒杀",
}

type UpetPTime struct {
	LogId          int32  `xorm:"not null pk autoincr comment('记录编号') INT(10)"`
	StartTime      int64  `xorm:"not null comment('开始时间') INT(10)"`
	EndTime        int64  `xorm:"not null comment('结束时间') INT(10)"`
	StoreId        int32  `xorm:"not null comment('店铺编号') INT(10)"`
	GoodsId        int32  `xorm:"not null comment('商品编号') unique(idx_goods_promotion) INT(10)"`
	PromotionId    int32  `xorm:"not null comment('促销编号') unique(idx_goods_promotion) INT(10)"`
	PromotionType  int32  `xorm:"not null comment('促销类型2限时折扣,3秒杀,4闪购5拼团') TINYINT(3)"`
	IsUpdate       int32  `xorm:"default 0 comment('是否已更新') TINYINT(1)"`
	PromotionPrice string `xorm:"not null comment('促销价格') DECIMAL(10,2)"`
}
