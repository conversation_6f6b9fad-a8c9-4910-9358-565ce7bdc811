package models

import (
	"time"
)

type UpetMemberBrowseLog struct {
	Id         int32     `xorm:"pk autoincr not null int 'id'"`
	SkuId      int32     `xorm:"default NULL comment('商品sku_id') int 'sku_id'"`
	SpuId      int32     `xorm:"default NULL comment('商品spu_id') int 'spu_id'"`
	MemberId   string    `xorm:"default 'NULL' comment('会员id') varchar(50) 'member_id'"`
	CreateTime time.Time `xorm:"not null comment('创建时间') datetime 'create_time'"`
	StoreId    int32     `xorm:"not null default 1 comment('小程序主体：1-默认，2-极宠家') int 'store_id'"`
}

type UpetMemberBrowseGoodInfo struct {
	Id          int32
	SkuId       int32
	SpuId       int32
	MemberId    string
	ProductName string
	Price       float64
	Spec        string
	UpDown      int32
	Pic         string
	StoreId     int64
	CreateTime  time.Time
}

func (u *UpetMemberBrowseLog) TableName() string {
	return "upet_member_browse_log"
}
