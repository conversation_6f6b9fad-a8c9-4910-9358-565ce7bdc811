package models

type UpetGoodsRelevanceSku struct {
	RelevanceSkuId             int32    `xorm:"not null pk autoincr INT(11)"`
	RelevanceId                int32    `xorm:"not null comment('关联ID') INT(11)"`
	RelevanceName              string   `xorm:"default NULL comment('关联名称') VARCHAR(100)"`
	RelevanceSpecifications    string   `xorm:"default '''' NULL comment('规格文字') VARCHAR(255)"`
	GoodsId                    int32    `xorm:"not NULL comment('商品ID') INT(11)"`
	GoodsName                  string   `xorm:"not NULL comment('商品名称（+规格名称）') VARCHAR(255)"`
	GoodsCommonid              int32    `xorm:"not NULL comment('商品公共表id') INT(11)"`
	GoodsSpec                  string   `xorm:"not NULL comment('商品规格序列化') TEXT(11)"`
	RelevanceSkuSort           int32    `xorm:"default 0 NULL comment('排序') INT(11)"`
	RelevanceSkuAddtime        int32    `xorm:"default 0 NULL comment('添加时间') INT(11)"`
	RelevanceSkuEdittime       int32    `xorm:"default 0 NULL comment('修改时间') INT(11)"`
}