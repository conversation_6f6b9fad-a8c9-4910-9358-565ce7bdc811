package models

import (
	"time"
)

// 活动表 结构体
type Promotion struct {
	// 活动id
	Id int32 `json:"id"`
	// 渠道id 多个用逗号隔开
	ChannelId string `json:"channel_id"`
	// 活动标题
	Title string `json:"title"`
	// 活动类型 11-秒杀
	Types int32 `json:"types"`
	// 活动状态 1-未开始 2-进行中 3-已结束
	Status int32 `json:"status"`
	// 是否系统结束 0否 1-是
	IsSystemEnd int32 `json:"is_system_end"`
	// 状态文本：未开始、进行中、已结束、已终止（status=3 && is_system_end=1）
	StatusText string `json:"status_text" xorm:"-"`
	// 活动开始时间
	BeginTime time.Time `json:"begin_time"`
	// 活动截止时间
	EndTime time.Time `json:"end_time"`
	//单笔购买上限 （0不限制）
	SeckillOrderLimit int32 `json:"seckill_order_limit"`
	// 是否免邮费 0否1是
	IsShippingFree int32 `json:"is_shipping_free"`
	// 是否显示 0否1是
	IsShow int32 `json:"is_show"`
	//// 创建人
	//CreateUserId string `json:"create_user_id"`
	//// 创建人名称
	//CreateUserName string `json:"create_user_name"`
	// 创建日期
	CreateTime time.Time `json:"create_time"`
	//// 最后更新人Id
	//UpdateUserId string `json:"update_user_id"`
	//// 最后更新人姓名
	//UpdateUserName string `json:"update_user_name"`
	// 最后更新时间
	UpdateTime time.Time `json:"update_time"`
}

// 活动产品表 结构体
type PromotionProduct struct {
	// 活动产品id
	Id int32 `json:"id"`
	// 产品spuid
	SpuId int32 `json:"spu_id"`
	// 商品skuId
	SkuId int32 `json:"sku_id"`
	// 活动id
	PromotionId int32 `json:"promotion_id"`
	// 活动类型  11-秒杀
	Types int32 `json:"types"`
	// 渠道
	ChannelId int32 `json:"channel_id"`
	// 商品名称
	ProductName string `json:"product_name"`
	// 上架下架:0-下架 1-上架
	UpDownState int32 `json:"up_down_state"`
	//商品原价
	MarketPrice int32 `json:"market_price"`
	//商品图片
	ProductImg string `json:"product_img"`
	// 秒杀价(单位分)
	SeckillPrice int32 `json:"seckill_price"`
	// 秒杀库存
	SeckillStock int32 `json:"seckill_stock"`
	// 创建日期
	CreateTime time.Time `json:"create_time"`
	// 最后更新时间
	UpdateTime time.Time `json:"update_time"`

	StoreId int32 `json:"store_id"`
}

// 活动日志表
type PromotionOptLog struct {
	// 操作的数据表名称
	TableName string
	//活动id
	PromotionId int32
	// 活动类型 11-秒杀
	PromotionType int32
	// 商品sku id
	SkuId int32
	// 操作类别：1创建，2编辑，3删除
	OptType int32
	//修改前的数据
	Content []byte
	// 操作者ID
	OpterId string
	// 操作者
	Opter string
	// 创建时间
	CreateTime time.Time
}
