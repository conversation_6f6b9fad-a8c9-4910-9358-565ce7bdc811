package models

import (
	"github.com/maybgit/glog"
)

type UpetGoodsClass struct {
	GcId          int64   `json:"gc_id" xorm:"pk autoincr not null comment('索引ID') INT(10) 'gc_id'"`
	GcName        string  `json:"gc_name" xorm:"not null comment('分类名称') VARCHAR(100) 'gc_name'"`
	TypeId        int64   `json:"type_id" xorm:"default 0 comment('类型id') INT(10) 'type_id'"`
	TypeName      string  `json:"type_name" xorm:"default '' comment('类型名称') VARCHAR(100) 'type_name'"`
	GcParentId    int64   `json:"gc_parent_id" xorm:"not null default 0 comment('父ID') INT(10) 'gc_parent_id'"`
	CommisRate    float64 `json:"commis_rate" xorm:"not null default 0 comment('佣金比例') FLOAT 'commis_rate'"`
	GcSort        int64   `json:"gc_sort" xorm:"not null default 0 comment('排序') TINYINT(3) 'gc_sort'"`
	GcVirtual     int64   `json:"gc_virtual" xorm:"not null default 0 comment('是否允许发布虚拟商品，1是，0否') TINYINT(3) 'gc_virtual'"`
	GcVerify      int64   `json:"gc_verify" xorm:"default 1 comment('是否可发布需要核销的虚拟商品 1-是 0否') TINYINT(1) 'gc_verify'"`
	GcNoverify    int64   `json:"gc_noverify" xorm:"default 'null' comment('是否可发布不需要核销的虚拟商品 1-是 0否') TINYINT(1) 'gc_noverify'"`
	GcTitle       string  `json:"gc_title" xorm:"default '' comment('名称') VARCHAR(200) 'gc_title'"`
	GcKeywords    string  `json:"gc_keywords" xorm:"default '' comment('关键词') VARCHAR(255) 'gc_keywords'"`
	GcDescription string  `json:"gc_description" xorm:"default '' comment('描述') VARCHAR(255) 'gc_description'"`
	ShowType      int64   `json:"show_type" xorm:"not null default 1 comment('商品展示方式，1按颜色，2按SPU') TINYINT(3) 'show_type'"`
	ClassType     int64   `json:"class_type" xorm:"default 0 comment('1猫站，2狗站') TINYINT(4) 'class_type'"`
}

// 根据多个id获取分类
func (u *UpetGoodsClass) FindByIds(ids []int64) (list []UpetGoodsClass, err error) {
	if err = GetDBConn().In("gc_id", ids).Find(&list); err != nil {
		glog.Error("UpetGoodsClass.FindByIds error: ", err)
	}
	return
}

// 根据多个父类id获取分类
func (u *UpetGoodsClass) FindByParentIds(ids []int64) (list []UpetGoodsClass, err error) {
	if err = GetDBConn().In("gc_parent_id", ids).Find(&list); err != nil {
		glog.Error("UpetGoodsClass.FindByParentIds error: ", err)
	}
	return
}
