package models

import (
	"time"
)

type UpetDisGoodsLog struct {
	Id         int64     `xorm:"not null pk autoincr INT(11)"`
	UserName   string    `xorm:"comment('操作人') VARCHAR(255)"`
	Spu        int       `xorm:"not null comment('商品spu') index INT(11)"`
	Sku        int       `xorm:"not null comment('商品id') index INT(11)"`
	Content    string    `xorm:"comment('操作内容') VARCHAR(512)"`
	CreatedAt  time.Time `xorm:"not null comment('操作时间') DATETIME"`
	ImportId   int       `xorm:"default 0 comment('关联批量导入id') index INT(11)"`
	ActivityId int64     `xorm:"default 0 comment('活动id') INT(11)"`
}
