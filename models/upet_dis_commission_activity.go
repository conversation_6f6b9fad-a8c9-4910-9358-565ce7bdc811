package models

import (
	"errors"
	"strings"
	"time"

	"github.com/go-xorm/xorm"
	kit "github.com/tricobbler/rp-kit"
)

type UpetDisCommissionActivity struct {
	Id         int64     `xorm:"not null pk autoincr INT(11)"`
	StoreId    int32     `json:"store_id"`    //店铺id
	Name       string    `json:"name"`        // 活动名称
	Status     int32     `json:"status"`      // 活动状态，1-进行中，2-未开始，3-已结束，4-已失效
	StartTime  time.Time `json:"start_time"`  // 活动开始时间
	EndTime    time.Time `json:"end_time"`    // 活动结束时间
	LastEditor string    `json:"last_editor"` // 最后编辑者
	CreatedAt  time.Time `json:"created_at"`  // 创建时间
	UpdatedAt  time.Time `json:"updated_at"`  // 更新时间
}

type UpetDisCommissionActivityGoods struct {
	Id           int64     `xorm:"not null pk autoincr INT(11)"`
	StoreId      int32     `json:"store_id"`      //店铺id
	SkuId        int64     `json:"sku_id"`        // 商品id
	Status       int32     `json:"status"`        // 商品状态，1-正常，2-删除
	ActivityId   int64     `json:"activity_id"`   // 限时佣金活动id
	ActivityRate float32   `json:"activity_rate"` // 活动佣金
	CreatedAt    time.Time `json:"created_at"`    // 创建时间
	UpdatedAt    time.Time `json:"updated_at"`    // 更新时间
}

// 活动商品表与商品表关联查询字段
type UpetCommissionActivityGoodsInfo struct {
	StoreId             int32   `json:"store_id"` //店铺id
	ActivityId          int64   `json:"activity_id"`
	SkuId               int64   `json:"sku_id"`
	ActivityRate        float32 `json:"activity_rate"`
	DisCommisRate       float32 `json:"dis_commis_rate"`
	DisNormalCommisRate float32 `json:"dis_normal_commis_rate"`
}

// 活动查询
func GetCommissionActivity(id int64) (out UpetDisCommissionActivity, err error) {
	if _, err = GetDBConn().ID(id).Get(&out); err != nil {
		return
	}
	if out.Id < 1 {
		err = errors.New("活动不存在！")
		return
	}
	return
}

// 获取活动下的商品
func GetCommissionActivitySkuIds(session *xorm.Session, activityId int64) (skuIds []string, err error) {
	err = session.SQL("SELECT sku_id FROM `upet_dis_commission_activity_goods` WHERE activity_id = ? AND status = ?", activityId, 1).Find(&skuIds)
	return
}

// 检测商品是否有参与其他活动
func CheckGoodsInOtherActivity(activityId int64, skuIds []string, sTime time.Time, eTime time.Time, orgId int32) (total int64, err error) {
	db := GetDBConn()

	st := sTime.Format(kit.DATETIME_LAYOUT)
	et := eTime.Format(kit.DATETIME_LAYOUT)
	sql := "SELECT count(*) as total FROM `upet_dis_commission_activity` a LEFT JOIN `upet_dis_commission_activity_goods` b ON a.id = b.activity_id" +
		" WHERE a.store_id = ? and b.store_id = ? and a.status IN (1,2) AND a.id != ? AND b.status = 1 AND b.sku_id IN (" + strings.Join(skuIds, ",") + ")" +
		" AND (? between a.start_time AND a.end_time OR ? between a.start_time AND a.end_time  OR a.start_time >= ? AND a.end_time <= ? )"
	_, err = db.SQL(sql, orgId, orgId, activityId, st, et, st, et).Get(&total)

	return
}
