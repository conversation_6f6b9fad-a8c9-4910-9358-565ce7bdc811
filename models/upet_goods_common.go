package models

type UpetGoodsCommon struct {
	GoodsCommonid        int     `xorm:"not null pk autoincr comment('商品公共表id') INT(10)"`
	GoodsName            string  `xorm:"not null comment('商品名称') VARCHAR(50)"`
	ShortName            string  `xorm:"not null comment('商品简称') VARCHAR(20)"`
	GoodsJingle          string  `xorm:"default '' comment('商品广告词') VARCHAR(150)"`
	GcId                 int     `xorm:"not null comment('商品分类') INT(10)"`
	GcId1                int     `xorm:"not null comment('一级分类id') INT(10)"`
	GcId2                int     `xorm:"not null comment('二级分类id') INT(10)"`
	GcId3                int     `xorm:"not null comment('三级分类id') INT(10)"`
	GcName               string  `xorm:"not null comment('商品分类') VARCHAR(200)"`
	StoreId              int     `xorm:"not null comment('店铺id') INT(10)"`
	StoreName            string  `xorm:"not null comment('店铺名称') VARCHAR(50)"`
	SpecName             string  `xorm:"not null comment('规格名称') VARCHAR(255)"`
	SpecValue            string  `xorm:"not null comment('规格值') TEXT"`
	BrandId              int     `xorm:"default 0 comment('品牌id') INT(10)"`
	BrandName            string  `xorm:"default '' comment('品牌名称') VARCHAR(100)"`
	TypeId               int     `xorm:"not null default 0 comment('类型id') INT(10)"`
	GoodsImage           string  `xorm:"not null comment('商品主图') VARCHAR(100)"`
	GoodsAttr            string  `xorm:"not null comment('商品属性') TEXT"`
	GoodsCustom          string  `xorm:"not null comment('商品自定义属性') TEXT"`
	GoodsBody            string  `xorm:"not null comment('商品内容') TEXT"`
	MobileBody           string  `xorm:"not null comment('手机端商品描述') TEXT"`
	GoodsUseBody         string  `xorm:"not null comment('使用范围') TEXT"`
	MobileUseBody        string  `xorm:"not null comment('手机端使用范围') TEXT"`
	GoodsPackageBody     string  `xorm:"not null comment('套餐明细') TEXT"`
	MobilePackageBody    string  `xorm:"not null comment('手机端套餐明细') TEXT"`
	GoodsState           int     `xorm:"not null comment('商品状态 0下架，1正常，10违规（禁售）') TINYINT(3)"`
	GoodsStateremark     string  `xorm:"comment('违规原因') VARCHAR(255)"`
	GoodsVerify          int     `xorm:"not null comment('商品审核 1通过，0未通过，10审核中') TINYINT(3)"`
	GoodsVerifyremark    string  `xorm:"comment('审核失败原因') VARCHAR(255)"`
	GoodsLock            int     `xorm:"not null default 0 comment('商品锁定 0未锁，1已锁') TINYINT(3)"`
	GoodsAddtime         int     `xorm:"not null comment('商品添加时间') INT(10)"`
	GoodsSelltime        int     `xorm:"not null comment('上架时间') INT(10)"`
	GoodsPrice           string  `xorm:"not null comment('商品价格') DECIMAL(10,2)"`
	GoodsMarketprice     string  `xorm:"not null comment('市场价') DECIMAL(10,2)"`
	GoodsCostprice       string  `xorm:"not null comment('成本价') DECIMAL(10,2)"`
	GoodsDiscount        float32 `xorm:"not null comment('折扣') FLOAT"`
	GoodsSerial          string  `xorm:"default '' comment('商品货号') VARCHAR(50)"`
	GoodsStorageAlarm    int     `xorm:"not null comment('库存报警值') TINYINT(3)"`
	GoodsBarcode         string  `xorm:"default '' comment('商品条形码') VARCHAR(20)"`
	TransportId          int     `xorm:"not null default 0 comment('运费模板') MEDIUMINT(8)"`
	TransportTitle       string  `xorm:"default '' comment('运费模板名称') VARCHAR(60)"`
	GoodsCommend         int     `xorm:"not null default 0 comment('商品推荐 1是，0否，默认为0') TINYINT(3)"`
	GoodsFreight         string  `xorm:"not null default 0.00 comment('运费 0为免运费') DECIMAL(10,2)"`
	GoodsVat             int     `xorm:"not null default 0 comment('是否开具增值税发票 1是，0否') TINYINT(3)"`
	Areaid1              int     `xorm:"not null comment('一级地区id') INT(10)"`
	Areaid2              int     `xorm:"not null comment('二级地区id') INT(10)"`
	GoodsStcids          string  `xorm:"default '' comment('店铺分类id 首尾用,隔开') VARCHAR(255)"`
	PlateidTop           int     `xorm:"comment('顶部关联板式') INT(10)"`
	PlateidBottom        int     `xorm:"comment('底部关联板式') INT(10)"`
	IsVirtual            int     `xorm:"not null default 0 comment('是否为虚拟商品 1是，0否') TINYINT(3)"`
	VirtualIndate        int     `xorm:"comment('虚拟商品有效期') INT(10)"`
	VirtualLimit         int     `xorm:"comment('虚拟商品购买上限') INT(10)"`
	VirtualInvalidRefund int     `xorm:"not null default 1 comment('是否允许过期退款， 1是，0否') TINYINT(3)"`
	VirtualRefundDay     int     `xorm:"comment('限制退款天数') TINYINT(4)"`
	SupId                int     `xorm:"not null comment('供应商id') INT(11)"`
	IsOwnShop            int     `xorm:"not null default 0 comment('是否为平台自营') TINYINT(3)"`
	GoodsTransV          string  `xorm:"not null default 0.00 comment('重量或体积') DECIMAL(10,2)"`
	IsDis                int     `xorm:"not null default 0 comment('是否分销') TINYINT(3)"`
	DisAddTime           int     `xorm:"not null default 0 comment('分销添加时间') INT(10)"`
	DisCommisRate        string  `xorm:"not null default 0.00 comment('分销佣金比例') DECIMAL(4,2)"`
	GoodsVideo           string  `xorm:"comment('商品视频') VARCHAR(300)"`
	SaleCount            int     `xorm:"default 0 comment('销量') INT(10)"`
	ClickCount           int     `xorm:"default 0 comment('点击量') INT(10)"`
	IsBatch              int     `xorm:"default 0 comment('是否批发商品 0零售  1批发') TINYINT(1)"`
	BatchPrice           string  `xorm:"comment('批发阶梯价') TEXT"`
	GoodsInv             int     `xorm:"not null default 1 comment('是否开发票') TINYINT(3)"`
	ChainBrandId         int     `xorm:"default 0 comment('商品归属品牌') INT(11)"`
	GSearchStatus        int     `xorm:"default 0 comment('是否是允许搜索商品 1否，0是') TINYINT(1)"`
	GCType               int     `xorm:"default 0 comment('1猫站，2狗站') TINYINT(4)"`
	Freight              int     `xorm:"default 0 comment('是否包邮0为不包邮，2为包邮') TINYINT(1)"`
	ChainId              int     `xorm:"default 0 comment('记录商品归属门店id') INT(11)"`
	RegionId             int     `xorm:"default 0 comment('大区标识') INT(11)"`
	DistriSort           int     `xorm:"default 0 comment('分销置顶，1为置顶0默认。') TINYINT(4)"`
	DistriGoodsSort      int     `xorm:"default 9999 comment('排序功能') INT(11)"`
	GoodsType            int     `xorm:"default 0 comment('0普通商品，1实实组合，2虚虚组合，3虚实组合') TINYINT(4)"`
	WarehouseType        int     `xorm:"comment('药品仓类型：0:默认否, 1:巨星药品仓') TINYINT(4)"`
	IsIntelGoods         int     `xorm:"default 0 comment('是否互联网医疗商品（1是，0否）') TINYINT(4)"`
}
