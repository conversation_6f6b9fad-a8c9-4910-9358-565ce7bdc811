package models

import (
	"errors"
	"github.com/go-xorm/xorm"
	"time"
)

type UpetGoods struct {
	GoodsId              int     `xorm:"not null pk autoincr comment('商品id(SKU)') INT(10)"`
	GoodsCommonid        int     `xorm:"not null comment('商品公共表id') index INT(10)"`
	GoodsName            string  `xorm:"not null comment('商品名称（+规格名称）') VARCHAR(50)"`
	ShortName            string  `xorm:"not null comment('商品短标题（+规格名称）') VARCHAR(50)"`
	GoodsJingle          string  `xorm:"default '' comment('商品广告词') VARCHAR(150)"`
	StoreId              int     `xorm:"not null comment('店铺id') INT(10)"`
	StoreName            string  `xorm:"not null comment('店铺名称') VARCHAR(50)"`
	GcId                 int     `xorm:"not null comment('商品分类id') INT(10)"`
	GcId1                int     `xorm:"not null comment('一级分类id') INT(10)"`
	GcId2                int     `xorm:"not null comment('二级分类id') INT(10)"`
	GcId3                int     `xorm:"not null comment('三级分类id') INT(10)"`
	BrandId              int     `xorm:"default 0 comment('品牌id') INT(10)"`
	GoodsPrice           string  `xorm:"not null comment('商品价格') index DECIMAL(10,2)"`
	GoodsPromotionPrice  string  `xorm:"not null comment('商品促销价格') DECIMAL(10,2)"`
	GoodsPromotionType   int     `xorm:"not null default 0 comment('促销类型 0无促销，1团购，2限时折扣 12水印') TINYINT(3)"`
	GoodsMarketprice     string  `xorm:"not null comment('市场价') DECIMAL(10,2)"`
	GoodsSerial          string  `xorm:"default '' comment('商品货号') index VARCHAR(50)"`
	GoodsStorageAlarm    int     `xorm:"not null comment('库存报警值') TINYINT(3)"`
	GoodsBarcode         string  `xorm:"default '' comment('商品条形码') VARCHAR(20)"`
	GoodsClick           int     `xorm:"not null default 0 comment('商品点击数量') INT(10)"`
	GoodsSalenum         int     `xorm:"not null default 0 comment('销售数量') INT(10)"`
	GoodsCollect         int     `xorm:"not null default 0 comment('收藏数量') INT(10)"`
	SpecName             string  `xorm:"not null comment('规格名称') VARCHAR(255)"`
	GoodsSpec            string  `xorm:"not null comment('商品规格序列化') TEXT"`
	GoodsStorage         int     `xorm:"not null default 0 comment('商品库存') INT(10)"`
	GoodsVirtualStorage  int     `xorm:"default 0 comment('虚拟库存') INT(10)"`
	GoodsImage           string  `xorm:"not null comment('商品主图') VARCHAR(5000)"`
	GoodsBody            string  `xorm:"not null comment('商品描述') TEXT"`
	MobileBody           string  `xorm:"not null comment('手机端商品描述') TEXT"`
	GoodsState           int     `xorm:"not null comment('商品状态 0下架，1正常，10违规（禁售）') TINYINT(3)"`
	GoodsVerify          int     `xorm:"not null comment('商品审核 1通过，0未通过，10审核中') TINYINT(3)"`
	GoodsAddtime         int     `xorm:"not null comment('商品添加时间') INT(10)"`
	GoodsEdittime        int     `xorm:"not null comment('商品编辑时间') INT(10)"`
	Areaid1              int     `xorm:"not null comment('一级地区id') INT(10)"`
	Areaid2              int     `xorm:"not null comment('二级地区id') INT(10)"`
	ColorId              int     `xorm:"not null default 0 comment('颜色规格id') INT(10)"`
	TransportId          int     `xorm:"not null comment('运费模板id') MEDIUMINT(8)"`
	GoodsFreight         string  `xorm:"not null default 0.00 comment('运费 0为免运费') DECIMAL(10,2)"`
	GoodsVat             int     `xorm:"not null default 0 comment('是否开具增值税发票 1是，0否') TINYINT(3)"`
	GoodsCommend         int     `xorm:"not null default 0 comment('商品推荐 1是，0否 默认0') TINYINT(3)"`
	GoodsStcids          string  `xorm:"default '' comment('店铺分类id 首尾用,隔开') VARCHAR(255)"`
	EvaluationGoodStar   int     `xorm:"not null default 5 comment('好评星级') TINYINT(3)"`
	EvaluationCount      int     `xorm:"not null default 0 comment('评价数') INT(10)"`
	IsVirtual            int     `xorm:"not null default 0 comment('是否为虚拟商品 1是，0否') TINYINT(3)"`
	VirtualIndate        int     `xorm:"not null comment('虚拟商品有效期') INT(10)"`
	VirtualLimit         int     `xorm:"not null comment('虚拟商品购买上限') INT(10)"`
	VirtualInvalidRefund int     `xorm:"not null default 1 comment('是否允许过期退款， 1是，0否') TINYINT(3)"`
	VirtualRefundDay     int     `xorm:"comment('限制退款天数') TINYINT(4)"`
	IsFcode              int     `xorm:"not null default 0 comment('是否为F码商品 1是，0否') TINYINT(4)"`
	IsPresell            int     `xorm:"not null default 0 comment('是否是预售商品 1是，0否') TINYINT(3)"`
	PresellDeliverdate   int     `xorm:"not null default 0 comment('预售商品发货时间') INT(11)"`
	IsBook               int     `xorm:"not null default 0 comment('是否为预定商品，1是，0否') TINYINT(4)"`
	BookDownPayment      string  `xorm:"not null default 0.00 comment('定金金额') DECIMAL(10,2)"`
	BookFinalPayment     string  `xorm:"not null default 0.00 comment('尾款金额') DECIMAL(10,2)"`
	BookDownTime         int     `xorm:"not null default 0 comment('预定结束时间') INT(11)"`
	BookBuyers           int     `xorm:"default 0 comment('预定人数') MEDIUMINT(9)"`
	HaveGift             int     `xorm:"not null default 0 comment('是否拥有赠品') TINYINT(3)"`
	IsOwnShop            int     `xorm:"not null default 0 comment('是否为平台自营') TINYINT(3)"`
	Contract1            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract2            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract3            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract4            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract5            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract6            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract7            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract8            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract9            int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	Contract10           int     `xorm:"not null default 0 comment('消费者保障服务状态 0关闭 1开启') TINYINT(1)"`
	IsChain              int     `xorm:"not null default 0 comment('是否为门店商品 1是，0否') TINYINT(3)"`
	GoodsTransV          string  `xorm:"not null default 0.00 comment('重量或体积') DECIMAL(10,2)"`
	IsDis                int     `xorm:"not null default 0 comment('是否分销') TINYINT(3)"`
	DisCommisRate        float32 `xorm:"not null default 0.00 comment('当前佣金比例') DECIMAL(4,2)"`
	DisActivityId        int     `xorm:"not null default 0 comment('参加限时佣金活动的id') INT(10)"`
	DisNormalCommisRate  float32 `xorm:"not null default 0.00 comment('日常佣金比例') DECIMAL(4,2)"`
	DisAddTime           int     `xorm:"not null default 0 comment('分销添加时间') INT(10)"`
	IsBatch              int     `xorm:"default 0 comment('是否批发商品 0零售  1批发') TINYINT(1)"`
	BatchPrice           string  `xorm:"comment('批发阶梯价') TEXT"`
	GoodsInv             int     `xorm:"not null default 1 comment('是否开发票') TINYINT(3)"`
	MemberPrice1         float32 `xorm:"not null default 0.00 comment('会员等级价v1') DECIMAL(10,2)"`
	MemberPrice2         float32 `xorm:"not null default 0.00 comment('会员等级价v2') DECIMAL(10,2)"`
	MemberPrice3         float32 `xorm:"not null default 0.00 comment('会员等级价v3') DECIMAL(10,2)"`
	GoodsLimit           int     `xorm:"default 0 comment('限购：0不限购') INT(11)"`
	GoodsRecommendNum    int     `xorm:"comment('APP推荐次数') INT(11)"`
	GSearchStatus        int     `xorm:"not null default 0 comment('是否是允许搜索商品 1否，0是') TINYINT(1)"`
	GCType               int     `xorm:"default 0 comment('1猫站，2狗站') TINYINT(4)"`
	Freight              int     `xorm:"default 0 comment('是否包邮0为不包邮，2为包邮') TINYINT(1)"`
	ChainId              int     `xorm:"default 0 comment('记录商品归属门店id') INT(11)"`
	RegionId             int     `xorm:"default 0 comment('大区标识') INT(11)"`
	GoodPercent          int     `xorm:"default 0 comment('好评率') TINYINT(4)"`
	GoodsSku             string  `xorm:"comment('中心货号') index VARCHAR(100)"`
	GoodsSkuType         int     `xorm:"default 1 comment('货号类型 1全渠道 2管易 3门店') TINYINT(1)"`
	IsVip                int     `xorm:"default 0 comment('是否限制会员卡用户购买，0否，1是') TINYINT(1)"`
	IsBzk                int     `xorm:"default 0 comment('是否限制保障卡用户购买，0否，1是') TINYINT(1)"`
	RelevanceId          int     `xorm:"default 0 comment('关联索引ID') INT(11)"`
	GoodsType            int     `xorm:"default 0 comment('0普通商品，1实实组合，2虚虚组合，3虚实组合') TINYINT(4)"`
	WarehouseType        int     `xorm:"comment('药品仓类型：0:默认否, 1:巨星药品仓') TINYINT(4)"`
	IsOpenVirtualStock   int     `xorm:"default 0 comment('是否开启虚拟库存，0默认关闭，1-开启') TINYINT(4)"`
	DisWrite             string  `xorm:"default '' comment('分销文案') VARCHAR(100)"`
	IsIntelGoods         int     `xorm:"default 0 comment('是否互联网医疗商品（1是，0否）') TINYINT(4)"`
}

// AddDis 添分销商品
func (sku *UpetGoods) AddDis(session *xorm.Session, rate float32, write string, orgId int32) (err error) {
	timestamp := time.Now().Unix()
	if _, err = session.Table("upet_goods_common").Where("goods_commonid = ? and is_dis = 0", sku.GoodsCommonid).Update(map[string]interface{}{
		"is_dis":            1,
		"dis_add_time":      timestamp,
		"distri_goods_sort": 9999,
		"distri_sort":       0,
	}); err != nil {
		return
	}

	ug := map[string]interface{}{
		"is_dis":                 1,
		"dis_add_time":           timestamp,
		"dis_commis_rate":        rate,
		"dis_normal_commis_rate": rate,
	}
	if len(write) > 0 {
		ug["dis_write"] = write
	}
	if _, err = session.Table("upet_goods").Where("goods_id = ? AND store_id=?", sku.GoodsId, orgId).Update(ug); err != nil {
		return
	}

	// 有唯一索引，出错不处理
	_, _ = session.Table("dis_goods_sta").Insert(map[string]interface{}{
		"store_id":        orgId,
		"goods_commonid":  sku.GoodsCommonid,
		"num_update_time": timestamp,
		"pay_update_time": timestamp,
	})
	return nil
}

// CancelDis 取消分销商品
func (sku *UpetGoods) CancelDis(session *xorm.Session, orgId int32) (err error) {
	if _, err = session.Table("upet_goods").Where("goods_id = ? AND store_id=?", sku.GoodsId, orgId).Update(map[string]interface{}{
		"is_dis":    0,
		"dis_write": "",
	}); err != nil {
		return
	}

	count := 0
	if _, err := session.Table("upet_goods").Where("goods_commonid = ? and is_dis = 1 and goods_id <> ? AND store_id=?", sku.GoodsCommonid, sku.GoodsId, orgId).
		Select("count(*)").Get(&count); err != nil {
		return errors.New("查询商品出错 " + err.Error())
	}
	if count > 0 {
		return
	}
	// spu下不存在分销商品，则取消spu分销状态
	_, err = session.Table("upet_goods_common").Where("goods_commonid = ?", sku.GoodsCommonid).Update(map[string]interface{}{
		"is_dis":            0,
		"dis_add_time":      0,
		"dis_commis_rate":   0,
		"distri_goods_sort": 9999,
		"distri_sort":       0,
	})
	return
}
