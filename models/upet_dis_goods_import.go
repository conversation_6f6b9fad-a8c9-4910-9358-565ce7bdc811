package models

import (
	"time"
)

type UpetDisGoodsImport struct {
	Id        int       `xorm:"not null pk autoincr INT(10)"`
	Type      int       `xorm:"not null default 0 comment('1批量导入商品、2批量导入限时佣金商品') index(type_type_id) TINYINT(4)"`
	TypeId    int       `xorm:"not null default 0 comment('对应类型id') index(type_type_id) INT(11)"`
	UserName  string    `xorm:"comment('操作人') VARCHAR(255)"`
	Result    string    `xorm:"comment('结果') VARCHAR(512)"`
	ResultUrl string    `xorm:"comment('结果excel链接') VARCHAR(512)"`
	CreatedAt time.Time `xorm:"not null comment('导入时间') DATETIME"`
}
