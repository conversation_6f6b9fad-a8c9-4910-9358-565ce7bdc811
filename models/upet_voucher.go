package models

import "github.com/maybgit/glog"

const (
	// 代金券状态(1-未用,2-已用,3-过期,4-收回)
	VoucherActiveDateUnused = iota + 1
	VoucherActiveDateUsed
	VoucherActiveDateExpired
	VoucherActiveDateRecover
)

type UpetVoucher struct {
	VoucherId         int64   `json:"voucher_id" xorm:"pk autoincr not null comment('代金券编号') INT(11) 'voucher_id'"`
	VoucherCode       string  `json:"voucher_code" xorm:"not null comment('代金券编码') VARCHAR(32) 'voucher_code'"`
	VoucherTId        int64   `json:"voucher_t_id" xorm:"not null comment('代金券模版编号') INT(11) 'voucher_t_id'"`
	VoucherTitle      string  `json:"voucher_title" xorm:"not null comment('代金券标题') VARCHAR(50) 'voucher_title'"`
	VoucherDesc       string  `json:"voucher_desc" xorm:"not null comment('代金券描述') VARCHAR(255) 'voucher_desc'"`
	VoucherStartDate  int64   `json:"voucher_start_date" xorm:"not null comment('代金券有效期开始时间') INT(11) 'voucher_start_date'"`
	VoucherEndDate    int64   `json:"voucher_end_date" xorm:"not null comment('代金券有效期结束时间') INT(11) 'voucher_end_date'"`
	VoucherPrice      int64   `json:"voucher_price" xorm:"not null comment('代金券面额') INT(11) 'voucher_price'"`
	VoucherLimit      float64 `json:"voucher_limit" xorm:"not null comment('代金券使用时的订单限额') DECIMAL(10) 'voucher_limit'"`
	VoucherStoreId    int64   `json:"voucher_store_id" xorm:"not null comment('代金券的店铺id') INT(11) 'voucher_store_id'"`
	VoucherState      int64   `json:"voucher_state" xorm:"not null comment('代金券状态(1-未用,2-已用,3-过期,4-收回)') TINYINT(4) 'voucher_state'"`
	VoucherActiveDate int64   `json:"voucher_active_date" xorm:"not null comment('代金券发放日期') INT(11) 'voucher_active_date'"`
	VoucherType       int64   `json:"voucher_type" xorm:"default 0 comment('代金券类别') TINYINT(4) 'voucher_type'"`
	VoucherOwnerId    int64   `json:"voucher_owner_id" xorm:"not null comment('代金券所有者id') INT(11) 'voucher_owner_id'"`
	VoucherOwnerName  string  `json:"voucher_owner_name" xorm:"not null comment('代金券所有者名称') VARCHAR(50) 'voucher_owner_name'"`
	VoucherOrderId    int64   `json:"voucher_order_id" xorm:"default 'null' comment('使用该代金券的订单编号') INT(11) 'voucher_order_id'"`
	VoucherPwd        string  `json:"voucher_pwd" xorm:"default 'null' comment('代金券卡密不可逆') VARCHAR(100) 'voucher_pwd'"`
	VoucherPwd2       string  `json:"voucher_pwd2" xorm:"default 'null' comment('代金券卡密2可逆') VARCHAR(100) 'voucher_pwd2'"`
	VoucherTType      int64   `json:"voucher_t_type" xorm:"default 0 comment('适用范围') TINYINT(4) 'voucher_t_type'"`
	VoucherTGcId      string  `json:"voucher_t_gc_id" xorm:"default '' comment('分类ID') VARCHAR(100) 'voucher_t_gc_id'"`
	VoucherSpecialId  int64   `json:"voucher_special_id" xorm:"default 0 comment('对应活动页面id') INT(11) 'voucher_special_id'"`
	VoucherBrandId    int64   `json:"voucher_brand_id" xorm:"default 0 comment('品牌id') INT(11) 'voucher_brand_id'"`
	VoucherVip        string  `json:"voucher_vip" xorm:"default '' comment('是否会员卡使用') VARCHAR(20) 'voucher_vip'"`
	VoucherFreeze     int64   `json:"voucher_freeze" xorm:"default 0 comment('是否冻结 0否 1是') TINYINT(1) 'voucher_freeze'"`
	VoucherFrom       int64   `json:"voucher_from" xorm:"default 0 comment('派券来源 0默认 1子龙办卡赠送,3新用户，4老用户，5支付成功赠送6 答题活动，7双旦活动抽奖，8宠物集市活动') TINYINT(1) 'voucher_from'"`
	WxCardCode        string  `json:"wx_card_code" xorm:"default 'null' comment('微信卡券code') VARCHAR(255) 'wx_card_code'"`
	WxCouponCode      string  `json:"wx_coupon_code" xorm:"default 'null' comment('微信商家券code') VARCHAR(255) 'wx_coupon_code'"`
	FromKey           string  `json:"from_key" xorm:"default 'null' comment('微页面领取key') VARCHAR(64) 'from_key'"`
}

func (m *UpetVoucher) RecoverUserVoucher(ownerId int64, tIds []int64) error {
	_, err := GetDBConn().Table("upet_voucher").Where("voucher_owner_id=? AND voucher_state=1", ownerId).In("voucher_t_id", tIds).
		Update(map[string]interface{}{
			"voucher_state": VoucherActiveDateRecover,
		})
	if err != nil {
		glog.Error("UpetVoucher.RecoverUserVoucher error: ", err)
	}
	return err
}

func (m *UpetVoucher) FindUsedListByVoucherTIds(tIds []int64, page, size int) ([]*UpetVoucher, error) {
	list := make([]*UpetVoucher, 0)
	err := GetDBConn().Table("upet_voucher").In("voucher_t_id", tIds).
		Where("voucher_state=?", VoucherActiveDateUsed).Limit(size, (page-1)*size).Find(&list)
	if err != nil {
		glog.Error("UpetVoucher.FindUsedListByVoucherTIds error: ", err)
	}
	return list, nil
}
