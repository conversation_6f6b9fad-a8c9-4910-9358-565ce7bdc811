package models

type UpetGoodsGift struct {
	GiftId         int    `xorm:"not null pk autoincr INT(10)"`
	GoodsId        int    `xorm:"not null INT(10)"`
	GoodsCommonid  int    `xorm:"not null INT(10)"`
	GiftGoodsid    int    `xorm:"not null INT(10)"`
	GiftGoodsname  string `xorm:"not null VARCHAR(50)"`
	GiftGoodsimage string `xorm:"not null VARCHAR(100)"`
	GiftAmount     int    `xorm:"not null SMALLINT(5)"`
	StartTime      string `xorm:"DATETIME"`
	EndTime        string `xorm:"DATETIME"`
	StoreId        int    `xorm:"not null INT(10)"`
}
