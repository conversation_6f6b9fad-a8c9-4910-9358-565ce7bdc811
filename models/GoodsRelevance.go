package models

type UpetGoodsRelevance struct {
	RelevanceId                int32    `xorm:"not null pk autoincr INT(11)"`
	RelevanceName              string   `xorm:"default NULL comment('关联名称') VARCHAR(100)"`
	RelevanceSpecifications    string   `xorm:"default '''' NULL comment('规格文字') VARCHAR(100)"`
	RelevanceAddtime           int32    `xorm:"default 0 NULL comment('创建时间') INT(11)"`
	RelevanceEdittime          int32    `xorm:"default 0 NULL comment('更新时间') INT(11)"`
	RelevanceVideo             string   `xorm:"default '''' NULL comment('关联主视频') VARCHAR(255)"`
	RelevanceImg               string   `xorm:"default '''' NULL comment('关联主图') VARCHAR(255)"`
}
