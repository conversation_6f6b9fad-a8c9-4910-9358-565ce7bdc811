package models

import (
	"encoding/json"
	"time"

	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
)

type IconInfo struct {
	LevelId int64  `json:"level_id"`
	Icon    string `json:"icon"`
}

// 会员权益表
type UserEquity struct {
	Id          int64     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	EquityName  string    `json:"equity_name" xorm:"not null default '' comment('权益名') VARCHAR(20) 'equity_name'"`
	Icon        string    `json:"icon" xorm:"not null default '' comment('权益图标,存JSON') TEXT 'icon'"`
	EquityInfo  string    `json:"equity_info" xorm:"default 'null' comment('权益简介') TEXT 'equity_info'"`
	EquityRules string    `json:"equity_rules" xorm:"default 'null' comment('规则') TEXT 'equity_rules'"`
	CreateTime  time.Time `json:"create_time" xorm:"created 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime  time.Time `json:"update_time" xorm:"updated 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`

	Icons []IconInfo `json:"icons" xorm:"-"`
}

// 解析数组icon
func (m *UserEquity) AfterSet(name string, cell xorm.Cell) {
	if name == "icon" {
		json.Unmarshal((*cell).([]byte), &m.Icons)
	}
}

// 获取用户等级关联的权益
func (m *UserEquity) FindByIds(ids []int64) ([]*UserEquity, error) {
	list := make([]*UserEquity, 0)
	err := GetDcDBConn().Table("user_equity").In("id", ids).Find(&list)
	if err != nil {
		glog.Error("UserEquity.FindByLevel error:", err)
	}
	return list, err
}
