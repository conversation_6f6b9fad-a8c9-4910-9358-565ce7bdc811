package models

import (
	"time"
)

type MemberMerge struct {
	MmlNotes        string    `json:"mml_notes"`
	MmlNewMobile    string    `json:"mml_new_mobile"`
	MmlOldMobile    string    `json:"mml_old_mobile"`
	MmlDealtime     int32     `json:"mml_dealtime"`
	CreateUser      string    `json:"create_user"`
	Applicant       string    `json:"applicant"`
	Department      string    `json:"department"`
	ApplicationDate time.Time `json:"application_date"`
	ApprovalRecord  string    `json:"approval_record"`
}
