package tasks

import (
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
)

// 定时任务
func InitTask() {
	//if kit.EnvCanCron() {
	if true {
		glog.Info("task run...")
		c := cron.New()

		// 每分钟执行一次"分 时 日 月 星期"， 检测定时任务是否执行
		c.AddFunc("*/1 * * * *", UpdateLimitActivityAndGoods)

		// 每天执行一次
		c.AddFunc("0 5 * * *", taskVoucherExpire)

		//每天凌晨00:05:00点执行赠品活动过期任务
		c.AddFunc("0 5 * * *", UpdateGiftGoods)

		c.Start()
	}
}
