package tasks

import (
	"_/models"
	"_/proto/common"
	"_/proto/dac"
	"encoding/json"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	"io/ioutil"
	"math/rand"
	"net/http"
	"time"
)

// 电商redis key带前缀upet_
var (
	activity920Key          = "upet_activity920:latest"
	activity920LotteryIdKey = "upet_activity920:latest:lottery_id"
	activity920IsUpdateKey  = "upet_activity920:latest:update" // 阿闻后台活动更新通知
	activity920Status       = "activity920:status"             // 活动状态，0-未开始，1-开始，2-结束
	activityExpiresTime     = time.Hour * 24 * 60              // 活动过期时间，60天
	redisClient             *redis.Client
	dbClient                *xorm.Engine
)

func init() {
	// 920锦鲤活动，每10秒钟检测活动是否开始或结束
	task := cron.New(cron.WithSeconds())
	if _, err := task.AddFunc("*/10 * * * * *", func() {
		TaskActivity920()
	}); err != nil {
		glog.Error("TaskActivity920 创建任务出错：", err.Error())
		time.Sleep(time.Second * 60)
	} else {
		task.Start()
	}
}

func TaskActivity920() {
	dbClient = models.GetDBConn()
	redisClient = common.GetRedisUpetConn()
	defer redisClient.Close()

	// 活动自动开奖
	go openReward()

	// 活动状态更新
	var lotteryDraw models.UpetLotteryDraw
	if _, err := dbClient.Select("id,lottery_state,lottery_start_time,lottery_end_time").OrderBy("id desc").Get(&lotteryDraw); err != nil {
		glog.Error("TaskActivity920查询最新活动出错：", err)
		return
	}
	if lotteryDraw.Id == 0 {
		return
	}

	// 活动状态更新
	curTime := time.Now().Unix()
	if curTime >= lotteryDraw.LotteryStartTime && curTime <= lotteryDraw.LotteryEndTime {
		// 进行中
		if lotteryDraw.LotteryState != 1 {
			_, err := dbClient.ID(lotteryDraw.Id).Cols("lottery_state").Update(&models.UpetLotteryDraw{LotteryState: 1})
			if err != nil {
				glog.Error("TaskActivity920更新活动状态为1出错：", err, kit.JsonEncode(lotteryDraw))
				return
			}
			redisClient.Set(activity920Status, 1, activityExpiresTime)
			updateActivity920LatestInfo(redisClient, lotteryDraw.Id)
		}
	} else if lotteryDraw.LotteryEndTime < curTime {
		// 已结束
		if lotteryDraw.LotteryState != 2 {
			redisClient.Set(activity920Status, 2, activityExpiresTime)
			_, err := dbClient.ID(lotteryDraw.Id).Cols("lottery_state").Update(&models.UpetLotteryDraw{LotteryState: 2})
			if err != nil {
				glog.Error("TaskActivity920更新活动状态为1出错：", err, kit.JsonEncode(lotteryDraw))
				return
			}
			updateActivity920LatestInfo(redisClient, lotteryDraw.Id)
		}
	}

	// 查询最新活动，存缓存
	data, _ := redisClient.Get(activity920Key).Result()
	isUpdate, _ := redisClient.Get(activity920IsUpdateKey).Result()
	if data == "" || cast.ToInt(isUpdate) == 1 {
		redisClient.Set(activity920Status, lotteryDraw.LotteryState, activityExpiresTime)
		updateActivity920LatestInfo(redisClient, lotteryDraw.Id)
	}
}

// 更新活动缓存数据
func updateActivity920LatestInfo(redis *redis.Client, lotteryId int) {
	host := config.GetString("ECUrl")
	url := fmt.Sprintf("%s/mobile/index.php?act=lottery_draw&op=getLotteryInfo&lottery_id=%d", host, lotteryId)
	resp, err := http.Get(url)
	if err != nil {
		glog.Error("TaskActivity920-http-Get出错:", err)
		return
	}

	body, err := ioutil.ReadAll(resp.Body)
	res := map[string]interface{}{}
	json.Unmarshal(body, &res)
	if code, _ := res["code"]; cast.ToInt32(code) == 200 {
		_, err = redis.Set(activity920Key, string(body), activityExpiresTime).Result()
		glog.Info("TaskActivity920更新updateActivity920LatestInfo记录：", err, lotteryId)
		redisClient.Set(activity920LotteryIdKey, lotteryId, activityExpiresTime)
		redisClient.Set(activity920IsUpdateKey, 0, activityExpiresTime)
	}
}

// 活动自动开奖，精确到分
func openReward() {
	var openTime models.UpetLotteryOpenTime
	_, err := dbClient.Where("open_time = ? and status = 0", time.Now().Format("2006-01-02 15:04:00")).Get(&openTime)
	if err != nil {
		glog.Error("TaskActivity920自动开奖openReward获取开奖信息出错：", err)
		return
	}
	if openTime.Id == 0 {
		return
	}
	totalRewardNum := openTime.RewardFirstNum + openTime.RewardSecondNum
	if openTime.Id == 0 || totalRewardNum == 0 {
		glog.Error("TaskActivity920自动开奖openReward失败，开奖id为0或开奖数量为0", openTime)
		return
	}
	if has, _ := redisClient.SetNX("activity920:openReward:"+cast.ToString(openTime.Id), 1, time.Minute*5).Result(); !has {
		return
	}

	/**
	日常开奖：同一幸运码不得重复参与日常开奖。即只要有一次获奖，不再参与剩下的日常开奖。每日获得的幸运码只能在当日参与日常开奖。日常开奖时间包括9月10日、9月13日、9月16日、9月23日，晚21点开奖。
	大奖开奖：同一幸运码可重复参与锦鲤大奖开奖。即日常开奖即便获奖，也可重复获得大奖。大奖开奖时间包括9月20日和9月26日，晚21点开奖。
	需求变更：
	中奖用户不得再次参加其他日子的抽奖，但920抽奖日可以参加，且920抽奖免单和奖品也可以同时被一人抽中。
	*/
	glog.Info("TaskActivity920自动开奖，期数：", openTime.Id)
	var sTime, eTime time.Time
	eTime = openTime.OpenTime
	if openTime.Type == 1 { // 日常开奖
		// 获取上一期开奖日期，组成一个活动区间段
		var nextOpentTime models.UpetLotteryOpenTime
		dbClient.Where("open_time < ?", openTime.OpenTime.Format(kit.DATETIME_LAYOUT)).OrderBy("open_time desc").Get(&nextOpentTime)
		if nextOpentTime.Id > 0 {
			sTime = nextOpentTime.OpenTime
		}
	}
	glog.Info("TaskActivity920自动开奖，期数：", openTime.Id, "时间：", sTime.Format(kit.DATETIME_LAYOUT), ":", eTime.Format(kit.DATETIME_LAYOUT))

	var lotteryNubmers, getRewards []models.UpetLotteryNumbers
	session := dbClient.NewSession()
	defer session.Close()

	// 获取满足条件的幸运码
	lotteryId, _ := redisClient.Get(activity920LotteryIdKey).Result()
	session.Where("lottery_id = ? and add_time <= ?", lotteryId, eTime.Unix())
	if sTime.Unix() > 1640966400 {
		session.Where("add_time > ?", sTime.Unix())
	}
	// 中奖用户不得再次参加其他日子的抽奖，但920抽奖日可以参加，且920抽奖免单和奖品也可以同时被一人抽中。
	rewardDate := openTime.OpenTime.Format("0102")
	if rewardDate != "0920" {
		session.Where("is_lottery = ? ", 0)
		var rewardMemberIds []string
		if err := dbClient.SQL("select member_id from upet_lottery_reward group by member_id").Find(&rewardMemberIds); err != nil {
			glog.Error("TaskActivity920自动开奖， 查询之前中奖用户失败，", err, lotteryId, sTime, eTime)
		} else if len(rewardMemberIds) > 0 {
			session.NotIn("member_id", rewardMemberIds)
		}
	}
	if err := session.Find(&lotteryNubmers); err != nil {
		glog.Error("TaskActivity920自动开奖， 获取幸运码失败，", err, lotteryId, sTime, eTime)
		return
	}
	if len(lotteryNubmers) == 0 {
		_, err = dbClient.Where("id = ?", openTime.Id).Cols("status, update_at").Update(&models.UpetLotteryOpenTime{Status: 1, UpdatedAt: time.Now()})
		if err != nil {
			glog.Error("TaskActivity920自动开奖，更新开奖状态失败：", err, kit.JsonEncode(openTime))
		}
		return
	}

	// 中奖用户筛选，随机参与用户的幸运码
	rand.Seed(time.Now().UnixNano())
	rand.Shuffle(len(lotteryNubmers), func(i, j int) {
		lotteryNubmers[i], lotteryNubmers[j] = lotteryNubmers[j], lotteryNubmers[i]
	})
	// 获取前openTime.RewardNum名为中奖号，一个用户只能中一个奖项，免单或奖品，920除外
	if rewardDate == "0920" {
		recordFreeMap := make(map[int64]int32)
		recordOtherMap := make(map[int64]int32)
		for k, _ := range lotteryNubmers {
			if len(getRewards) >= totalRewardNum {
				break
			}
			// 个奖项只允许中一次，可以重复
			if len(getRewards) <= openTime.RewardFirstNum { // 免单
				if _, hasFree := recordFreeMap[lotteryNubmers[k].MemberId]; hasFree {
					continue
				}
				recordFreeMap[lotteryNubmers[k].MemberId] = 1
			} else {
				if _, has := recordOtherMap[lotteryNubmers[k].MemberId]; has {
					continue
				}
				recordOtherMap[lotteryNubmers[k].MemberId] = 1
			}
			getRewards = append(getRewards, lotteryNubmers[k])
		}
	} else { // 非920的，一个用户只能中一个奖项
		recordMap := make(map[int64]int32)
		for k, _ := range lotteryNubmers {
			if len(getRewards) >= totalRewardNum {
				break
			}
			if _, has := recordMap[lotteryNubmers[k].MemberId]; has {
				continue
			}
			getRewards = append(getRewards, lotteryNubmers[k])
			recordMap[lotteryNubmers[k].MemberId] = 1
		}
	}

	var (
		members   []models.UpetMember
		memberIds []string
	)
	// 批量获取用户信息
	memberMap := make(map[int64]models.UpetMember)
	for k, _ := range getRewards {
		memberIds = append(memberIds, cast.ToString(getRewards[k].MemberId))
	}
	if err := dbClient.Select("member_id,member_avatar, member_mobile").In("member_id", memberIds).Find(&members); err != nil {
		glog.Error("TaskActivity920自动开奖，中奖信息批量获取用户信息失败：", err, kit.JsonEncode(memberIds))
	}
	for _, member := range members {
		memberMap[member.MemberId] = member
	}

	// 中奖数据插入
	for k, _ := range getRewards {
		lotteryReward := models.UpetLotteryReward{
			LotteryId:     cast.ToInt(getRewards[k].LotteryId),
			MemberId:      getRewards[k].MemberId,
			LotteryNumber: getRewards[k].LotteryNumber,
			OpenTimeId:    openTime.Id,
			AddTime:       time.Now(),
		}
		if k < openTime.RewardFirstNum {
			lotteryReward.RewardId = 1
		} else if k < totalRewardNum {
			lotteryReward.RewardId = 2
		}
		if info, has := memberMap[getRewards[k].MemberId]; has {
			lotteryReward.Mobile = info.MemberMobile
			lotteryReward.Avatar = info.MemberAvatar
		}
		if _, err := dbClient.Insert(&lotteryReward); err != nil {
			glog.Error("TaskActivity920自动开奖，中奖信息插入失败：", err, kit.JsonEncode(lotteryReward))
		} else {
			// 中奖短信发送
			go sendRewardSms(lotteryReward.RewardId, openTime.OpenTime.Format("1月02日"), lotteryReward.Mobile)
			_, err = dbClient.Where("id = ?", getRewards[k].Id).Cols("is_lottery").Update(&models.UpetLotteryNumbers{IsLottery: 1})
			if err != nil {
				glog.Error("TaskActivity920自动开奖，幸运码中奖状态更新失败：", err, kit.JsonEncode(lotteryReward))
			}
		}
	}

	_, err = dbClient.Where("id = ?", openTime.Id).Cols("status, updated_at").Update(&models.UpetLotteryOpenTime{Status: 1, UpdatedAt: time.Now()})
	if err != nil {
		glog.Error("TaskActivity920自动开奖，中更新开奖状态失败：", err, kit.JsonEncode(openTime))
	}
}

// 中奖短信发送
func sendRewardSms(rewardId int32, rewardDate, mobile string) {
	in := dac.SendSmsRequest{
		PhoneNumbers:  mobile,
		SignName:      config.GetString("activity92_sms_sign_name"),
		TemplateCode:  config.GetString("activity92_sms_template_code"),
		TemplateParam: kit.JsonEncode(map[string]string{"time": rewardDate}),
	}

	daClient := dac.GetDataCenterClient()
	res, err := daClient.RPC.SendSms(daClient.Ctx, &in)
	if err != nil {
		glog.Error("sendRewardSms发送中奖信息失败，手机号：", mobile, "，中奖类型：", rewardId, "，中奖期数：", rewardDate, "，错误信息：", err.Error(), "，返回值：", kit.JsonEncode(res))
	} else {
		glog.Info("sendRewardSms发送中奖信息成功，手机号：", mobile, "，中奖类型：", rewardId, "，中奖期数：", rewardDate, "，返回值：", kit.JsonEncode(res))
	}
}
