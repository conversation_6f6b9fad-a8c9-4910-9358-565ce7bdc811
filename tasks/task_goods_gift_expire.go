package tasks

import (
	"_/models"
	"_/utils"
	"github.com/maybgit/glog"
	"time"
)

// upet_goods_gift表中的赠品数据，如果活动过期，更新upet_goods表中的goods_gift字段,并删除upet_goods_gift赠品数据
// 获取过期的赠品数据
// 更新upet_goods表中的goods_gift字段
// 删除upet_goods_gift赠品数据
func UpdateGiftGoods() {
	glog.Info("UpdateGiftGoods start")
	//初始化redis
	redisConn := utils.ConnectRedis()
	defer redisConn.Close()

	taskLock := "upetcenter:task:UpdateGiftGoodsLock"
	if !redisConn.SetNX(taskLock, time.Now().Unix(), 12*time.Hour).Val() {
		glog.Error("UpdateGiftGoods already running")
		return
	}
	defer redisConn.Del(taskLock)

	curTime := time.Now()
	//获取过期的赠品数据
	list := make([]*models.UpetGoodsGift, 0)
	err := models.GetDBConn().Table("upet_goods_gift").Where("end_time<?", curTime).Find(&list)
	if err != nil {
		glog.Error("UpdateGiftGoods error: ", err)
		return
	}
	if len(list) == 0 {
		return
	}

	//更新upet_goods表中的goods_gift字段，用sql语句更新
	//update upet_goods g left join upet_goods_gift gg on g.goods_id = gg.goods_id and g.store_id = gg.store_id set g.have_gift = 0 where gg.end_time < now() and g.have_gift = 1;
	_, err = models.GetDBConn().Exec("update upet_goods g left join upet_goods_gift gg on g.goods_id = gg.goods_id and g.store_id = gg.store_id set g.have_gift = 0 where gg.end_time < ? and g.have_gift = 1", curTime)
	if err != nil {
		glog.Error("UpdateGiftGoods update error: ", err)
		return
	}
	//因为一个goods_id 可以对应多个赠品，如果有效还要更新have_gift=1
	_, err = models.GetDBConn().Exec("update upet_goods g left join upet_goods_gift gg on g.goods_id = gg.goods_id and g.store_id = gg.store_id set g.have_gift = 1 where gg.start_time < now() and gg.end_time > now() and g.have_gift = 0")
	if err != nil {
		glog.Error("UpdateGiftGoods update2  error: ", err)
		return
	}

	//删除upet_goods_gift赠品数据
	_, err = models.GetDBConn().Table("upet_goods_gift").Where("end_time<?", curTime).Delete(&models.UpetGoodsGift{})
	if err != nil {
		glog.Error("UpdateGiftGoods delete error: ", err)
		return
	}

	glog.Info("UpdateGiftGoods end")
}
