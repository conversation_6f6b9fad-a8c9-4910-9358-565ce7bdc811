package tasks

import (
	"_/models"
	"_/services"
	"sync"
	"time"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

// 更新显示活动商品
func UpdateLimitActivityAndGoods() {
	sTime := time.Now()
	glog.Info("限时活动任务开始:", time.Now().Format(kit.DATETIME_LAYOUT))

	var wg sync.WaitGroup
	wg.Add(2)

	// 活动开始
	go func() {
		defer wg.Done()
		session := models.GetDBConn().NewSession()
		defer session.Close()

		curTime := time.Now().Format(services.MinuteLayout) + ":00"
		var activity []*models.UpetDisCommissionActivity
		err := session.Where("status = ? AND start_time <= ?  AND end_time > ?", services.LimitActivityUnStart, curTime, curTime).Find(&activity)
		if err != nil {
			glog.Error("限时佣金，活动开始查询失败，", err.Error())
			return
		}
		if len(activity) < 1 {
			return
		}

		for _, v := range activity {
			// 让活动先更新为进行中
			if _, err = session.ID(v.Id).Update(&models.UpetDisCommissionActivity{
				Status:    services.LimitActivityDoing,
				UpdatedAt: time.Now(),
			}); err != nil {
				glog.Error("限时佣金，活动开始失败，活动"+cast.ToString(v.Id)+"状态更新失败，", err.Error())
				continue
			}

			session.Begin()
			// 查询商品，更新活动佣金
			var activityGoodsInfo []*models.UpetCommissionActivityGoodsInfo
			sql := "SELECT a.store_id,a.id as activity_id,a.sku_id,a.activity_rate,b.dis_commis_rate,b.dis_normal_commis_rate FROM `upet_dis_commission_activity_goods` a LEFT JOIN `upet_goods` b ON a.sku_id = b.goods_id and a.store_id=b.store_id " +
				" WHERE a.activity_id = ? AND  a.status = 1 AND b.goods_state = 1 AND b.is_dis = 1"
			if err = session.SQL(sql, v.Id).Find(&activityGoodsInfo); err != nil {
				glog.Error("限时佣金，活动开始失败，活动"+cast.ToString(v.Id)+"查询商品出错了，", err.Error())
				continue
			}
			if len(activityGoodsInfo) < 1 {
				continue
			}

			for k, _ := range activityGoodsInfo {
				updateData := models.UpetGoods{
					DisActivityId: cast.ToInt(activityGoodsInfo[k].ActivityId),
					DisCommisRate: activityGoodsInfo[k].ActivityRate,
				}
				if activityGoodsInfo[k].DisNormalCommisRate <= 0.01 { // 日常佣金为0才去更新
					updateData.DisNormalCommisRate = activityGoodsInfo[k].DisCommisRate
				}
				if _, err = session.Where("goods_id = ? AND store_id=? ", activityGoodsInfo[k].SkuId, activityGoodsInfo[k].StoreId).Update(&updateData); err != nil {
					session.Rollback()
					glog.Error("限时佣金，活动开始失败，活动"+cast.ToString(v.Id)+"商品"+cast.ToString(activityGoodsInfo[k].SkuId)+"更新失败，", err.Error())
					continue
				}
			}
			session.Commit()
		}
	}()

	// 活动结束
	go func() {
		defer wg.Done()
		session := models.GetDBConn().NewSession()
		defer session.Close()

		var activityFinish []*models.UpetDisCommissionActivity
		err := session.Where("status = ? AND end_time <= ?", services.LimitActivityDoing, time.Now().Format(services.MinuteLayout)+":00").Find(&activityFinish)
		if err != nil {
			glog.Error("限时佣金，活动结束查询失败，", err.Error())
			return
		}
		if len(activityFinish) < 1 {
			return
		}

		for _, v := range activityFinish {
			// 让活动先更新为结束状态
			if _, err = session.ID(v.Id).Update(&models.UpetDisCommissionActivity{
				Status:    services.LimitActivityFinish,
				UpdatedAt: time.Now(),
			}); err != nil {
				glog.Error("限时佣金，活动结束失败，活动"+cast.ToString(v.Id)+"状态更新失败，", err.Error())
				continue
			}

			session.Begin()
			// 查询商品，更新为日常佣金
			var activityGoodsInfo []*models.UpetCommissionActivityGoodsInfo
			sql := "SELECT a.store_id,a.id as activity_id,a.sku_id,a.activity_rate,b.dis_commis_rate,b.dis_normal_commis_rate FROM `upet_dis_commission_activity_goods` a LEFT JOIN `upet_goods` b ON a.sku_id = b.goods_id and a.store_id=b.store_id " +
				" WHERE a.activity_id = ?"
			if err = session.SQL(sql, v.Id).Find(&activityGoodsInfo); err != nil {
				glog.Error("限时佣金，活动结束失败，活动"+cast.ToString(v.Id)+"查询商品出错了，", err.Error())
				continue
			}
			if len(activityGoodsInfo) < 1 {
				continue
			}

			for k, _ := range activityGoodsInfo {
				updateData := models.UpetGoods{
					DisActivityId: 0,
					DisCommisRate: activityGoodsInfo[k].DisNormalCommisRate,
				}
				if _, err = session.Where("goods_id = ? AND store_id=? ", activityGoodsInfo[k].SkuId, activityGoodsInfo[k].StoreId).Cols("dis_commis_rate,dis_activity_id").Update(&updateData); err != nil {
					session.Rollback()
					glog.Error("限时佣金，活动结束失败，活动"+cast.ToString(v.Id)+"商品"+cast.ToString(activityGoodsInfo[k].SkuId)+"更新失败，", err.Error())
					continue
				}
			}
			session.Commit()
		}
	}()

	wg.Wait()

	glog.Info("限时活动任务结束:", time.Now().Format(kit.DATETIME_LAYOUT), "耗时:", time.Now().Sub(sTime))
}
