package tasks

import (
	"_/models"
	"_/proto/cc"
	"_/proto/mc"
	"_/utils"
	"fmt"
	"time"

	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
)

type UserVoucherCount struct {
	VoucherOwnerId int64     `json:"voucher_owner_id"`
	Count          int64     `json:"count"`
	VoucherEndDate time.Time `json:"voucher_end_date"`
	VoucherStoreId int32     `json:"voucher_store_id"`
	VoucherTitle   string    `json:"voucher_title"`
}

// 商城券过期3天前提醒
func taskVoucherExpire() {
	glog.Info("taskVoucherExpire start")
	//初始化redis
	redisConn := utils.ConnectRedis()
	defer redisConn.Close()

	taskLock := "upetcenter:task:taskVoucherExpireLock"
	if !redisConn.SetNX(taskLock, time.Now().Unix(), 12*time.Hour).Val() {
		glog.Error("taskVoucherExpire already running")
		return
	}
	defer redisConn.Del(taskLock)

	curTime := time.Now()
	st3 := time.Date(curTime.Year(), curTime.Month(), curTime.Day()+3, 0, 0, 0, 0, time.Local).Unix()
	et3 := time.Date(curTime.Year(), curTime.Month(), curTime.Day()+3, 23, 59, 59, 0, time.Local).Unix()

	startId := int64(0)
	for {
		list := make([]*UserVoucherCount, 0)
		err := models.GetDBConn().Table("upet_voucher").
			Select("voucher_owner_id,count(*) count,min(voucher_end_date) voucher_end_date,voucher_store_id,voucher_title").
			Where("voucher_owner_id>? AND voucher_state=? AND voucher_end_date>=? AND voucher_end_date<=?", startId, 1, st3, et3).
			GroupBy("voucher_owner_id,voucher_store_id").OrderBy("voucher_owner_id ASC").Find(&list)
		if err != nil {
			glog.Error("taskVoucherExpire error: ", err)
			return
		}
		if len(list) == 0 {
			break
		}

		for _, v := range list {
			startId = v.VoucherOwnerId

			member := &models.UpetMember{}
			b, err := models.GetDBConn().Table("upet_member").Cols("member_id,scrm_user_id,weixin_mini_openid,weixin_mini_openid2").Where("member_id=?", v.VoucherOwnerId).Get(member)
			if err != nil {
				glog.Error("taskVoucherExpire upet_member error: ", err)
				return
			}
			if !b {
				continue
			}

			// 发送提醒
			sendVoucherExpireWxMessage(member, v.Count, v.VoucherEndDate, v.VoucherStoreId, v.VoucherTitle)
		}
		time.Sleep(time.Millisecond * 200)
	}

	glog.Info("taskVoucherExpire end")
}

// 发送微信订阅消息，优惠券过期提醒
func sendVoucherExpireWxMessage(member *models.UpetMember, count int64, expireTime time.Time, orgId int32, voucherTitle string) {
	subTemplate, err := getVoucherExpireSubscribeMessageTemplate(orgId)
	if err != nil {
		glog.Error("sendVoucherExpireWxMessage error: ", err, member.ScrmUserId)
		return
	}

	// 是否能推送订阅消息
	ccClient := cc.GetCustomerCenterClient()
	defer ccClient.Close()
	canRe, _ := ccClient.RPC.CanSendWechatSubscribeMessage(ccClient.Ctx, &cc.CanSendWechatSubscribeMessageReq{
		ScrmUserId:    member.ScrmUserId,
		SubscribeType: "voucher",
		TemplateId:    subTemplate.TemplateId,
	})
	if canRe.Code != 200 {
		glog.Info("sendVoucherExpireWxMessage CanSendWechatSubscribeMessage ", member.ScrmUserId, kit.JsonEncode(canRe))
		return
	}

	// 会员等级变更
	miniprogramState := ""
	if kit.EnvIsTest() {
		miniprogramState = "trial"
	}
	data := fmt.Sprintf(subTemplate.Content, fmt.Sprintf("您有%d张优惠券即将过期，请尽快使用!", count), expireTime.Format("2006年01月02日"))
	// 确定openid，默认和极宠家
	openId := member.WeixinMiniOpenid
	if orgId == 2 {
		openId = member.WeixinMiniOpenid2
		data = fmt.Sprintf(subTemplate.Content, voucherTitle, "请及时使用，即将过期", expireTime.Format("2006年01月02日"))
	} else if orgId == 3 {
		openId, err = getEshopUser(member.MemberId, 3)
		if err != nil {
			glog.Error("sendIntegralExpireMsg 获取用户福码购小程序的openid异常 error: ", err, member.MemberId)
			return
		}
	}
	msgClient := mc.GetMessageCenterClient()
	re, err := msgClient.RPC.SubscribeMessage(msgClient.Ctx, &mc.SubscribeMessageRequest{
		Touser:           openId,
		TemplateId:       subTemplate.TemplateId,
		Page:             subTemplate.Page, // 小程序跳转页面
		MiniprogramState: miniprogramState,
		Data:             data,
		IsJpush:          0,
		OrgId:            orgId,
	})
	if err != nil {
		glog.Error("sendWxMessage error: ", err, member.ScrmUserId)
		return
	}
	if re.Code != 200 {
		glog.Error("发送优惠券过期通知失败，", member.ScrmUserId, kit.JsonEncode(re))
		return
	}

	// 订阅数减一
	_, err = models.GetDcDBConn().Exec("UPDATE wechat_subscribe_message SET number=IF(number<1,0,number-1) WHERE scrm_user_id=? AND template_id=?", member.ScrmUserId, subTemplate.TemplateId)
	if err != nil {
		glog.Error("扣减用户订阅数失败，error: ", err, member.ScrmUserId)
		return
	}
}

func getEshopUser(memberId int64, orgId int) (string, error) {
	var miniOpenId string
	_, err := models.GetDcDBConn().SQL("SELECT weixin_mini_openid FROM eshop.users Where member_id=? AND org_id=?", memberId, orgId).Get(&miniOpenId)
	if err != nil {
		glog.Error("获取福码购小程序的用户openId失败")
		return "", err
	}
	return miniOpenId, nil
}

// 优惠券过期订阅模板内容
var voucherExpireSubscribeMessageTemplate *models.WechatSubscribeMessageTemplate

// 获取订阅消息模板
func getVoucherExpireSubscribeMessageTemplate(orgId int32) (*models.WechatSubscribeMessageTemplate, error) {
	if voucherExpireSubscribeMessageTemplate != nil {
		return voucherExpireSubscribeMessageTemplate, nil
	}
	voucherExpireSubscribeMessageTemplate = &models.WechatSubscribeMessageTemplate{}
	_, err := models.GetDcDBConn().Table("wechat_subscribe_message_template").
		Where("template_key=? AND store_id=?", "user-voucher-expire", orgId).
		Get(voucherExpireSubscribeMessageTemplate)
	if err != nil {
		glog.Error("userLevelExpireTask user-level-change error: ", err)
		return nil, err
	}
	return voucherExpireSubscribeMessageTemplate, nil
}
